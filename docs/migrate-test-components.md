# migrate-test-components 使用指南

基于现有test-project的第三方组件迁移工具，专门针对test-project中实际使用的组件进行精确迁移。

## 🎯 设计理念

与通用的third-party-migrator不同，`migrate-test-components`专门针对test-project中的实际组件使用情况：

- **精确映射**：基于test-project中实际使用的组件进行映射
- **实用性强**：针对真实代码场景，不是理论迁移
- **测试友好**：可以在test-project上安全测试迁移效果
- **学习价值**：通过实际案例学习组件迁移

## 📦 支持的组件

基于test-project/src/views/Components.vue中的实际使用：

| 组件 | 当前版本 | 目标版本 | 使用位置 | 迁移复杂度 |
|------|----------|----------|----------|------------|
| vue-count-to | ^1.0.13 | vue3-count-to | Components.vue:228, TestComponent.vue:14 | 简单 |
| vuedraggable | ^4.1.0 | vuedraggable@4.x | Components.vue:229, TestComponent.vue:15 | 中等 |
| vue-splitpane | ^1.0.6 | splitpanes | Components.vue:230 | 中等 |
| @riophae/vue-treeselect | ^0.4.0 | Element Plus | Components.vue:231 | 复杂 |
| v-charts | ^1.19.0 | vue-echarts | Components.vue:234 | 复杂 |
| vue-json-pretty | - | vue-json-pretty@^2 | Components.vue:236 | 简单 |
| vue-scrollbars | - | vue3-perfect-scrollbar | Components.vue:238 | 中等 |
| vue-uuid | ^2.0.2 | vue3-uuid | Components.vue:239 | 复杂 |

## 🚀 快速开始

### 1. 分析项目
```bash
# 分析test-project中的组件使用情况
node bin/migrate-test-components.js analyze

# 详细分析
node bin/migrate-test-components.js analyze --verbose

# 输出分析结果到文件
node bin/migrate-test-components.js analyze --output analysis.json
```

### 2. 生成迁移计划
```bash
# 生成迁移计划
node bin/migrate-test-components.js plan

# 指定输出文件
node bin/migrate-test-components.js plan --output my-migration-plan.md
```

### 3. 测试迁移规则
```bash
# 测试Components.vue文件
node bin/migrate-test-components.js test-rules src/views/Components.vue

# 测试特定组件
node bin/migrate-test-components.js test-rules src/views/Components.vue --component vue-count-to

# 详细输出
node bin/migrate-test-components.js test-rules src/views/Components.vue --verbose
```

### 4. 执行迁移
```bash
# 预览迁移（推荐先运行）
node bin/migrate-test-components.js migrate --dry-run

# 执行实际迁移
node bin/migrate-test-components.js migrate

# 只使用规则引擎（不使用AI）
node bin/migrate-test-components.js migrate --use-rules-only

# 只迁移特定组件
node bin/migrate-test-components.js migrate --component vue-count-to
```

## 📋 详细命令说明

### analyze 命令
分析test-project中的第三方组件使用情况。

```bash
node bin/migrate-test-components.js analyze [options]

Options:
  --test-project <path>  test-project路径 (默认: ./test-project)
  --verbose             详细输出
  --output <file>       输出分析结果到JSON文件
```

**输出示例**：
```
📊 分析结果:
发现组件: 8
涉及文件: 3

需要迁移的组件:
📦 vue-count-to → vue3-count-to
  文件数: 2
    - src/views/Components.vue (medium, 1处使用)
    - src/components/TestComponent.vue (simple, 1处使用)
```

### migrate 命令
执行第三方组件迁移。

```bash
node bin/migrate-test-components.js migrate [options]

Options:
  --test-project <path>  test-project路径 (默认: ./test-project)
  --dry-run             预览模式，不实际修改文件
  --verbose             详细输出
  --component <name>    只迁移指定组件
  --ai-api-key <key>    AI API密钥
  --use-rules-only      只使用规则引擎，不使用AI
```

### test-rules 命令
测试特定文件的迁移规则。

```bash
node bin/migrate-test-components.js test-rules <file-path> [options]

Arguments:
  file-path             相对于test-project的文件路径

Options:
  --test-project <path>  test-project路径 (默认: ./test-project)
  --component <name>     指定组件名称
  --verbose             详细输出
```

### plan 命令
生成迁移计划文档。

```bash
node bin/migrate-test-components.js plan [options]

Options:
  --test-project <path>  test-project路径 (默认: ./test-project)
  --output <file>       输出计划到文件 (默认: migration-plan.md)
```

## 🔧 迁移策略

### 1. 规则引擎优先
对于简单的组件（如vue-count-to），优先使用规则引擎：
- 成本低（无AI调用）
- 速度快
- 结果确定

### 2. AI辅助复杂迁移
对于复杂组件（如@riophae/vue-treeselect），使用AI：
- 理解上下文
- 处理复杂API变更
- 生成适配代码

### 3. 分组迁移
按复杂度分组处理：
1. **简单组件**：vue-count-to, vue-json-pretty
2. **中等组件**：vuedraggable, vue-splitpane, vue-scrollbars
3. **复杂组件**：@riophae/vue-treeselect, v-charts, vue-uuid

## 📊 迁移报告

迁移完成后会生成详细报告：

```
📊 Test Project 迁移报告:
成功: ✅
消息: 迁移完成
总文件数: 3
处理文件数: 3
AI迁移: 1
规则迁移: 2
跳过文件: 0
错误数: 0
```

## 🧪 测试验证

迁移后建议进行以下测试：

### 1. 功能测试
```bash
cd test-project
npm run serve
```
访问 http://localhost:8080/components 检查组件功能

### 2. 构建测试
```bash
cd test-project
npm run build
```

### 3. 单元测试
```bash
cd test-project
npm run test:unit
```

## 💡 最佳实践

### 1. 迁移前准备
- 确保代码已提交到版本控制
- 备份test-project目录
- 了解各组件的API变更

### 2. 分步迁移
```bash
# 1. 先分析
node bin/migrate-test-components.js analyze --verbose

# 2. 生成计划
node bin/migrate-test-components.js plan

# 3. 测试规则
node bin/migrate-test-components.js test-rules src/views/Components.vue

# 4. 预览迁移
node bin/migrate-test-components.js migrate --dry-run

# 5. 执行迁移
node bin/migrate-test-components.js migrate
```

### 3. 迁移后验证
- 检查所有组件是否正常渲染
- 测试交互功能
- 验证样式是否正确
- 运行完整测试套件

## 🔍 故障排除

### 常见问题

**Q: 提示"test-project路径不存在"**
A: 确保在项目根目录运行命令，或使用--test-project参数指定正确路径

**Q: 某些组件迁移失败**
A: 检查AI API密钥是否配置，或使用--use-rules-only只进行规则迁移

**Q: 迁移后组件不工作**
A: 检查package.json是否已更新依赖版本，运行npm install安装新依赖

### 调试选项
```bash
# 详细输出
--verbose

# 预览模式
--dry-run

# 只测试规则
--use-rules-only
```

## 🔗 相关工具

- `third-party-migrator`: 通用第三方组件迁移工具
- `vue-migrator`: Vue 2到Vue 3完整迁移工具
- `build-fixer`: 构建错误修复工具

## 📚 学习资源

- [Vue 3迁移指南](https://v3-migration.vuejs.org/)
- [组件迁移最佳实践](./component-migration-best-practices.md)
- [test-project组件使用说明](../test-project/README.md)
