# 依赖升级指南

## 概述

PackageUpgrader 是 Vue 2 到 Vue 3 迁移工具中的依赖管理组件，它能够智能地处理依赖版本升级和兼容性问题。

## 主要功能

### 1. 版本兼容性检查

自动检测项目中的版本兼容性问题，特别是：

- **html-webpack-plugin** 版本过低导致的 `getHooks is not a function` 错误
- **script-ext-html-webpack-plugin** 与 html-webpack-plugin 5.x 的不兼容问题

### 2. 自动依赖升级

- 升级关键依赖到兼容版本
- 移除不兼容的依赖
- 添加必要的 Vue 3 依赖

### 3. 配置文件自动修复

- 自动修改 `vue.config.js` 文件
- 移除不兼容的插件配置
- 清理相关的 import 语句

## 使用方法

### 基本用法

```javascript
const PackageUpgrader = require('./src/dependency/packageUpgrader')

const upgrader = new PackageUpgrader('./my-project', {
  autoFixConfig: true, // 自动修复配置文件
  preserveVue3Dependencies: true // 保留已有的 Vue 3 依赖
})

// 执行升级
const result = await upgrader.upgrade()
```

### 选项说明

- `autoFixConfig`: 是否自动修复配置文件（默认: true）
- `preserveVue3Dependencies`: 是否保留已有的 Vue 3 依赖（默认: true）
- `migrationMode`: 是否为迁移模式（默认: false）

## 处理的问题

### 1. htmlWebpackPlugin.getHooks is not a function

**问题描述**: 当 `html-webpack-plugin` 版本低于 5.x 时，会出现此错误。

**解决方案**: 
- 自动升级 `html-webpack-plugin` 到 `^5.6.0`
- 移除不兼容的 `script-ext-html-webpack-plugin`

**示例输出**:
```
⚠️  检测到版本兼容性问题:
  ❌ html-webpack-plugin: htmlWebpackPlugin.getHooks is not a function
     💡 解决方案: Upgrade to version 5.6.0 or higher for webpack 5 compatibility
  ❌ script-ext-html-webpack-plugin: Incompatible with html-webpack-plugin 5.x
     💡 解决方案: Remove this plugin and update vue.config.js configuration

🔧 正在自动处理关键兼容性问题...
  ✅ 已升级 html-webpack-plugin 到 ^5.6.0
✅ 关键兼容性问题已自动修复
```

### 2. 配置文件修改

**自动修改 vue.config.js**:
- 移除 `script-ext-html-webpack-plugin` 的 require 语句
- 移除相关的插件配置代码

**修改前**:
```javascript
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin')

module.exports = {
  chainWebpack: config => {
    config
      .plugin('ScriptExtHtmlWebpackPlugin')
      .after('html')
      .use('script-ext-html-webpack-plugin', [{
        defaultAttribute: 'defer'
      }])
  }
}
```

**修改后**:
```javascript
module.exports = {
  chainWebpack: config => {
    // script-ext-html-webpack-plugin 配置已移除
  }
}
```

## 配置说明

### package-recommend.json

配置文件定义了依赖的升级规则：

```json
{
  "needsUpgrade": {
    "html-webpack-plugin": {
      "version": "^5.6.0",
      "note": "Required for webpack 5 compatibility, fixes getHooks error",
      "description": "HTML webpack plugin",
      "critical": true
    }
  },
  "knownIncompatible": {
    "script-ext-html-webpack-plugin": {
      "description": "Script extension for HTML webpack plugin"
    }
  }
}
```

- `critical`: 标记为关键依赖，需要优先处理
- `version`: 目标升级版本
- `note`: 升级说明和注意事项

## 测试

运行测试来验证功能：

```bash
npm test -- --testNamePattern="PackageUpgrader"
```

## 注意事项

1. **备份**: 建议在执行升级前备份项目文件
2. **手动检查**: 自动修复后，建议手动检查配置文件的修改结果
3. **依赖安装**: 升级完成后需要重新安装依赖 (`npm install` 或 `pnpm install`)

## 故障排除

### 常见问题

1. **配置文件修改失败**
   - 检查文件权限
   - 确认文件格式正确
   - 查看错误日志

2. **依赖升级失败**
   - 检查网络连接
   - 确认 package.json 格式正确
   - 查看具体的错误信息

### 获取帮助

如果遇到问题，可以：
1. 查看控制台输出的详细错误信息
2. 检查生成的日志文件
3. 提交 issue 并提供详细的错误信息 