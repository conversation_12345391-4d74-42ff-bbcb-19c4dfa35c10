# Vue 2 到 Vue 3 迁移常见问题 FAQ

## 登录相关问题

### Q1: 默认登录失败，找不到 `.login-form` 选择器
**问题描述**: 验证工具尝试自动登录时，找不到预期的登录表单元素。

**常见原因**:
- 登录页面使用了不同的 CSS 类名
- 登录表单结构与预期不符
- 页面还未完全加载

**解决方案**:
1. 检查实际登录页面的 HTML 结构
2. 修改 AutoLoginManager 中的选择器
3. 使用 AI 生成的登录逻辑（推荐）

### Q2: AI 生成的登录逻辑失败
**问题描述**: AI 分析登录页面后生成的登录步骤执行失败。

**常见原因**:
- 页面有验证码或其他安全措施
- 输入框的 name 属性与预期不符
- 登录按钮的选择器不正确

**解决方案**:
1. 检查页面截图，确认页面结构
2. 手动配置登录逻辑
3. 跳过需要登录的页面验证

## 路由解析问题

### Q3: 路由解析不完整，只找到部分路由
**问题描述**: 路由解析器只能找到 constantRoutes 中的路由，asyncRoutes 中的路由被忽略。

**常见原因**:
- 路由配置使用了动态导入
- asyncRoutes 没有被正确合并到路由器中
- 路由模块导入有问题

**解决方案**:
1. 检查 router/index.js 中的路由合并逻辑
2. 确保 asyncRoutes 被正确导出和使用
3. 使用 AI 解析复杂的路由结构

### Q4: 路由到组件的映射不正确
**问题描述**: AI 无法找到正确的组件文件路径。

**常见原因**:
- 组件路径使用了别名（如 @/）
- 动态导入的路径解析失败
- 文件实际路径与推断路径不符

**解决方案**:
1. 使用路由映射功能自动建立映射关系
2. 手动指定组件文件路径
3. 改进路径推断算法

## 组件错误问题

### Q5: Vue 3 组件语法错误
**问题描述**: Vue 2 组件在 Vue 3 中出现语法错误。

**常见错误**:
- `this.$router` 改为 `useRouter()`
- `this.$store` 改为 `useStore()`
- 生命周期钩子名称变化
- 组合式 API 语法

**解决方案**:
1. 使用 AI 自动修复组件语法
2. 参考 Vue 3 迁移指南
3. 逐步迁移到组合式 API

### Q6: Element UI 到 Element Plus 迁移问题
**问题描述**: Element UI 组件在 Element Plus 中不兼容。

**常见问题**:
- 组件名称变化
- 属性名称变化
- 事件名称变化
- 样式类名变化

**解决方案**:
1. 使用官方迁移工具
2. 手动替换组件引用
3. 更新组件属性和事件

## 构建配置问题

### Q7: Webpack 配置错误
**问题描述**: Vue 3 项目的 Webpack 配置与 Vue 2 不兼容。

**常见问题**:
- Vue Loader 版本不兼容
- 插件配置错误
- 别名配置问题

**解决方案**:
1. **避免直接修改配置文件**（推荐）
2. 优先修改源代码适配现有配置
3. 只在必要时才修改 vue.config.js

### Q8: 依赖版本冲突
**问题描述**: Vue 3 相关依赖与现有依赖冲突。

**解决方案**:
1. 使用包管理器解决依赖冲突
2. 更新到兼容版本
3. 移除不兼容的依赖

## 运行时错误问题

### Q9: 组件渲染错误
**问题描述**: 组件在运行时出现渲染错误。

**常见原因**:
- 响应式数据定义错误
- 模板语法不兼容
- 组件生命周期问题

**解决方案**:
1. 使用 AI 分析错误并自动修复
2. 检查组件的响应式数据定义
3. 更新模板语法

### Q10: 路由导航错误
**问题描述**: 页面路由导航失败或重定向错误。

**解决方案**:
1. 检查路由配置
2. 更新路由导航方法
3. 修复路由守卫逻辑

## 最佳实践

### 验证工具使用建议
1. **优先使用指定路由验证**: `--routes "/charts/keyboard,/dashboard"`
2. **启用详细输出**: `--verbose`
3. **使用自动修复**: `--auto-fix`
4. **保持浏览器可见**: `--no-headless`

### AI 修复建议
1. **避免修改配置文件**: 优先修改源代码
2. **分批修复**: 一次修复少量文件
3. **验证修复效果**: 修复后重新验证
4. **保留备份**: 自动备份原文件

### 调试技巧
1. 查看页面截图了解实际状态
2. 检查 AI 调用日志了解分析过程
3. 使用浏览器开发者工具调试
4. 逐步验证单个页面

## 工具参数说明

### 简化后的参数
```bash
node bin/page-validator.js check <project-path> [options]

Options:
  -b, --base-url <url>     外部服务器地址 (default: "http://localhost:9527")
  --router-mode <mode>     Vue Router模式: hash 或 history (default: "hash")
  --no-headless           显示浏览器界面
  --auto-fix              启用自动错误修复
  --routes <routes>       指定要测试的路由，用逗号分隔
  -v, --verbose           详细输出
  --username <username>   登录用户名 (default: "admin")
  --password <password>   登录密码 (default: "111111")
```

### 常用命令示例
```bash
node bin/page-validator.js check /Users/<USER>/works/galaxy/galaxy-vue3-demi --verbose --auto-fix --no-headless --routes "/charts/keyboard"
```
