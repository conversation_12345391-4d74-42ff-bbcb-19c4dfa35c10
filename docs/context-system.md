# Context 系统设计文档

## 概述

Context 系统是 Vue Migrate 项目的核心上下文管理解决方案，提供了统一的状态管理、事件通信和组件协调机制。该系统旨在解决复杂迁移过程中的状态跟踪、错误处理和组件间通信问题。

## 核心组件

### 1. MigrationContext

迁移上下文是整个系统的核心数据结构，负责管理迁移过程中的所有状态信息。

**主要功能：**
- 项目信息管理（路径、类型、版本等）
- 迁移配置管理（模式、选项、AI 配置等）
- 阶段状态跟踪（当前阶段、已完成阶段、失败阶段）
- 文件状态管理（处理进度、修改记录、失败文件）
- 依赖关系跟踪（原始依赖、升级依赖、冲突处理）
- AI 服务状态（调用次数、成功率、错误记录）
- 构建状态监控（构建结果、错误信息、修复尝试）
- 错误和警告收集
- 统计信息汇总

**使用示例：**
```javascript
const context = new MigrationContext('/path/to/project', {
  mode: 'auto',
  verbose: true
});

// 设置项目信息
context.setProjectInfo({
  name: 'my-vue-app',
  type: 'vue',
  detectedFramework: 'Vue 2'
});

// 管理阶段
context.startPhase('dependency-upgrade');
// ... 执行升级逻辑
context.completePhase('dependency-upgrade', { upgraded: 5 });

// 记录错误
context.addError(new Error('Build failed'), 'build-phase');

// 导出状态
const summary = context.getStatusSummary();
```

### 2. ContextManager

上下文管理器负责协调多个迁移上下文，提供全局的事件通信和状态管理。

**主要功能：**
- 多上下文管理（创建、删除、切换）
- 全局事件系统（发布/订阅模式）
- 上下文快照和恢复
- 健康状态监控
- 统计信息汇总

**使用示例：**
```javascript
const { contextManager } = require('./src/core/ContextManager');

// 创建上下文
const { contextId, context } = contextManager.createContext('/path/to/project');

// 订阅事件
contextManager.subscribe('phase:complete', (data) => {
  console.log(`阶段完成: ${data.phase}`);
});

// 健康检查
const health = contextManager.healthCheck();
```

### 3. ContextAwareComponent

上下文感知组件基类，为所有迁移组件提供统一的上下文访问能力。

**主要功能：**
- 自动上下文注入
- 生命周期管理（初始化、执行、清理）
- 事件通信（发布/订阅）
- 依赖管理（等待其他组件完成）
- 错误处理和状态跟踪

**使用示例：**
```javascript
class CustomMigrator extends ContextAwareComponent {
  constructor(options = {}) {
    super('CustomMigrator', options);
  }

  async execute() {
    const projectPath = this.getProjectPath();
    const config = this.getConfig();
    
    // 执行迁移逻辑
    const result = await this.performMigration();
    
    // 更新上下文
    this.updateFileStatus('src/App.vue', { modified: true });
    
    return result;
  }

  onSetupEventListeners() {
    this.subscribe('dependency:updated', (data) => {
      // 响应依赖更新事件
    });
  }
}
```

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    ContextManager                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Context 1     │  │   Context 2     │  │   Context N  │ │
│  │                 │  │                 │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ Events & State
                              │
┌─────────────────────────────────────────────────────────────┐
│                 ContextAwareComponents                      │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────────┐   │
│  │ AutoMigrator │  │ BuildFixer   │  │ DependencyMapper │   │
│  └──────────────┘  └──────────────┘  └──────────────────┘   │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────────┐   │
│  │ CodeMigrator │  │ AIRepairer   │  │ ProjectDetector  │   │
│  └──────────────┘  └──────────────┘  └──────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 事件系统

Context 系统使用发布/订阅模式进行组件间通信：

### 核心事件类型

- `phase:start` - 阶段开始
- `phase:complete` - 阶段完成
- `phase:failed` - 阶段失败
- `file:processed` - 文件处理完成
- `ai:call` - AI 调用事件
- `build:status:updated` - 构建状态更新
- `dependencies:updated` - 依赖更新
- `error:added` - 错误添加
- `migration:complete` - 迁移完成

### 事件订阅示例

```javascript
// 全局订阅
contextManager.subscribe('phase:complete', (data) => {
  console.log(`阶段 ${data.phase} 完成`);
});

// 组件内订阅
class MyComponent extends ContextAwareComponent {
  onSetupEventListeners() {
    this.subscribe('file:processed', (data) => {
      this.handleFileProcessed(data);
    });
  }
}
```

## 状态管理

### 状态层次结构

```
MigrationContext
├── project (项目信息)
├── config (配置信息)
├── phases (阶段状态)
├── files (文件状态)
├── dependencies (依赖状态)
├── ai (AI 服务状态)
├── build (构建状态)
├── issues (错误和警告)
├── stats (统计信息)
└── tools (工具实例)
```

### 状态访问模式

```javascript
// 读取状态
const projectInfo = context.project;
const currentPhase = context.phases.current;
const errorCount = context.issues.errors.length;

// 更新状态
context.setProjectInfo({ type: 'vue3' });
context.updateBuildStatus({ status: 'success' });
context.addError(new Error('Something went wrong'));
```

## 最佳实践

### 1. 组件设计

- 继承 `ContextAwareComponent` 基类
- 实现 `execute()` 方法包含主要逻辑
- 使用 `beforeExecute()` 和 `afterExecute()` 进行前后置处理
- 通过 `onSetupEventListeners()` 设置事件监听

### 2. 错误处理

- 使用 `addError()` 记录错误到上下文
- 区分关键错误和一般错误
- 提供详细的错误上下文信息

### 3. 状态更新

- 及时更新文件处理状态
- 记录 AI 调用结果
- 维护构建状态的准确性

### 4. 事件通信

- 使用语义化的事件名称
- 提供充分的事件数据
- 避免事件循环依赖

## 集成指南

### 现有组件迁移

1. 修改组件继承 `ContextAwareComponent`
2. 将构造函数参数传递给父类
3. 实现 `execute()` 方法
4. 移除手动状态管理代码
5. 使用上下文 API 访问共享状态

### 新组件开发

1. 继承 `ContextAwareComponent`
2. 定义组件名称和选项
3. 实现必要的生命周期方法
4. 设置事件监听器
5. 使用上下文 API 进行状态管理

## 性能考虑

- 事件订阅会消耗内存，及时清理不需要的订阅
- 大量状态更新可能影响性能，考虑批量更新
- 上下文快照功能适合调试，生产环境谨慎使用
- 健康检查功能有一定开销，不要频繁调用

## 调试和监控

### 日志记录

Context 系统提供详细的日志记录：

```javascript
// 启用详细日志
const context = new MigrationContext(projectPath, { verbose: true });

// 查看状态摘要
console.log(context.getStatusSummary());

// 导出完整状态
const fullState = context.export();
```

### 健康检查

```javascript
const health = contextManager.healthCheck();
if (health.status !== 'healthy') {
  console.warn('检测到问题:', health.issues);
}
```

### 性能监控

```javascript
const stats = contextManager.getStatistics();
console.log('上下文统计:', stats);
```

## 未来扩展

- 支持分布式迁移（多进程/多机器）
- 增加更细粒度的权限控制
- 实现上下文版本控制和回滚
- 添加可视化监控界面
- 支持插件化的事件处理器
