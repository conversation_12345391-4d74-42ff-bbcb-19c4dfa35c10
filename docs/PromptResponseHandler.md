# PromptResponseHandler - 统一的提示词和响应处理器

PromptResponseHandler 是一个统一的XML处理类，用于格式化提示词中的XML模板和解析AI响应中的XML内容。它解决了 BuildFixAgent 和 PromptBuilder 中XML格式不一致的问题。

## 主要功能

- **格式化XML模板** - 为PromptBuilder生成标准化的XML示例
- **解析AI响应** - 从AI响应中提取XML内容，支持多种格式
- **从markdown代码块中提取XML** - 自动处理```xml代码块
- **统一处理不同的XML格式** - 支持多种XML结构
- **HTML实体解码** - 自动处理&lt;、&gt;等编码

## 支持的XML格式

### 1. fix_result 格式
用于文件修复响应：
```xml
<fix_result>
<fixed_content>
修复后的完整文件内容
</fixed_content>
<changes_made>
简要说明所做的修改
</changes_made>
</fix_result>
```

### 2. analysis 格式
用于错误分析响应：
```xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
```

### 3. file_fix 格式（向后兼容）
旧版本的文件修复格式：
```xml
<file_fix>
<content>
修复后的文件内容
</content>
</file_fix>
```

### 4. tool_calls 格式
工具调用格式（支持JSON和XML两种）：
```json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "src/App.vue"
      }
    }
  ],
  "reasoning": "说明为什么需要这些工具调用"
}
```

## 基本用法

### 初始化
```javascript
const PromptResponseHandler = require('./src/ai/PromptResponseHandler');

const responseHandler = new PromptResponseHandler({
  verbose: true,  // 启用详细日志
  maxContentLength: 10000  // 最大内容长度
});
```

### 格式化模板
```javascript
// 生成 fix_result 格式的示例
const template = responseHandler.formatTemplate(
  'fix_result',
  '修复后的完整文件内容',
  '简要说明所做的修改'
);

// 生成 analysis 格式的示例
const analysisTemplate = responseHandler.formatTemplate(
  'analysis',
  '<file>src/App.vue</file>\n<file>src/main.js</file>',
  '这些文件包含 Vue 2 语法需要升级'
);
```

### 解析AI响应
```javascript
// 解析任意格式的响应
const result = responseHandler.parseResponse(aiResponse);

if (result.success) {
  console.log('格式:', result.format);
  console.log('内容:', result.content);
  console.log('元数据:', result.metadata);
}

// 解析特定格式的响应
const fixResult = responseHandler.parseResponse(aiResponse, 'fix_result');
```

### 解析文件列表
```javascript
const analysisResult = responseHandler.parseResponse(response, 'analysis');
if (analysisResult.success) {
  const files = responseHandler.parseFileList(analysisResult.content);
  console.log('需要修复的文件:', files);
}
```

### 解析工具调用
```javascript
const toolCallResult = responseHandler.parseToolCalls(response);
if (toolCallResult.success) {
  console.log('工具调用:', toolCallResult.toolCalls);
  console.log('推理说明:', toolCallResult.reasoning);
}
```

### 统一响应解析
```javascript
// 自动检测并解析不同类型的响应
const result = responseHandler.parseAnyResponse(response, 'fix_result');
const fileListResult = responseHandler.parseAnyResponse(response, 'file_list');
const toolCallResult = responseHandler.parseAnyResponse(response, 'tool_calls');
```

## 在BuildFixAgent中的使用

```javascript
class BuildFixAgent {
  constructor(projectPath, options = {}) {
    // 初始化XML处理器
    this.responseHandler = new PromptResponseHandler(this.options);
  }

  parseFixResponse(response, originalContent) {
    // 使用PromptResponseHandler解析响应
    const parseResult = this.responseHandler.parseResponse(response);
    
    if (parseResult.success) {
      const validationResult = this.validateFixedContent(parseResult.content, originalContent);
      if (validationResult) {
        return {
          success: true,
          newContent: validationResult,
          fixedContent: validationResult,
          format: parseResult.format,
          metadata: parseResult.metadata
        };
      }
    }

    // 回退到旧的解析逻辑
    return this.parseFixResponseLegacy(response, originalContent);
  }
}
```

## 在PromptBuilder中的使用

```javascript
class PromptBuilder {
  constructor(toolRegistry, options = {}) {
    // 初始化XML处理器
    this.responseHandler = new PromptResponseHandler(this.options);
  }

  getFileFixTemplate() {
    return `...
**响应格式**：
请使用以下格式返回修复结果：

\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整文件内容', '简要说明所做的修改')}
\`\`\`

请提供完整的修复后文件内容，不要省略任何部分。`;
  }
}
```

## 错误处理

PromptResponseHandler 提供了完善的错误处理机制：

```javascript
const result = responseHandler.parseResponse(response);

if (!result.success) {
  console.error('解析失败:', result.error);
  // 可以尝试其他解析方法或回退逻辑
}
```

## 格式验证

```javascript
// 验证XML格式是否正确
const isValid = responseHandler.validateXmlFormat(xmlContent, 'fix_result');

// 获取支持的格式列表
const formats = responseHandler.getSupportedFormats();
console.log('支持的格式:', formats);
```

## 最佳实践

1. **统一使用PromptResponseHandler** - 在所有需要XML处理的地方使用同一个实例
2. **指定期望格式** - 在解析时指定期望的格式以提高准确性
3. **错误处理** - 始终检查解析结果的success字段
4. **回退机制** - 为旧格式提供向后兼容的解析逻辑
5. **日志记录** - 在开发时启用verbose模式以便调试

## 扩展新格式

要添加新的XML格式，只需在PromptResponseHandler的构造函数中添加格式定义：

```javascript
this.formats = {
  // 现有格式...
  
  // 新格式
  new_format: {
    tag: 'new_format',
    contentTag: 'content',
    metaTag: 'metadata'
  }
};
```

这样就可以统一处理BuildFixAgent和PromptBuilder中的XML格式，确保一致性和可维护性。
