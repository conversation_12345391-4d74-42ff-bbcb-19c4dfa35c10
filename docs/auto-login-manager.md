# AutoLoginManager - 自动登录管理器

## 概述

AutoLoginManager 是一个智能的自动登录管理器，支持多种登录策略，包括默认登录逻辑和 AI 生成的登录逻辑。

## 功能特性

1. **多层登录策略**
   - 默认登录逻辑（基于常见的表单结构）
   - 配置文件登录逻辑（保存成功的登录步骤）
   - AI 生成登录逻辑（分析 DOM 结构自动生成）

2. **智能配置管理**
   - 自动保存成功的登录配置
   - JSON 格式存储登录步骤
   - 支持配置文件复用

3. **AI 集成**
   - 使用 GLM API 分析登录页面
   - 自动生成 Puppeteer 可执行的登录步骤
   - 支持复杂登录表单的自动识别

## 使用方法

### 1. 命令行使用

```bash
# 使用默认用户名密码
node bin/page-validator.js check /path/to/project

# 指定用户名和密码
node bin/page-validator.js check /path/to/project --username myuser --password mypass

# 详细输出模式
node bin/page-validator.js check /path/to/project --username admin --password 123456 --verbose
```

### 2. 编程使用

```javascript
const AutoLoginManager = require('./src/runtime-validation/AutoLoginManager');

// 创建登录管理器
const loginManager = new AutoLoginManager({
  username: 'admin',
  password: '123456',
  verbose: true,
  aiEnabled: true,
  configPath: './.login-config.json'
});

// 在 Puppeteer 页面中使用
const success = await loginManager.attemptLogin(page);
if (success) {
  console.log('登录成功！');
} else {
  console.log('登录失败');
}
```

## 配置选项

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `username` | string | 'admin' | 登录用户名 |
| `password` | string | '111111' | 登录密码 |
| `verbose` | boolean | false | 是否输出详细日志 |
| `aiEnabled` | boolean | true | 是否启用 AI 功能 |
| `configPath` | string | '.login-config.json' | 配置文件路径 |
| `maxRetries` | number | 3 | 最大重试次数 |

## 登录流程

1. **尝试默认登录逻辑**
   - 查找常见的登录表单元素
   - 填写用户名和密码
   - 点击登录按钮

2. **使用保存的配置**
   - 加载之前成功的登录配置
   - 执行保存的登录步骤

3. **AI 生成登录逻辑**
   - 分析页面 DOM 结构
   - 调用 AI 生成登录步骤
   - 执行 AI 生成的步骤
   - 保存成功的配置供下次使用

## 登录步骤格式

登录步骤使用 JSON 格式存储，支持以下操作类型：

```json
[
  {
    "action": "wait",
    "selector": "input[name='username']",
    "timeout": 5000
  },
  {
    "action": "type",
    "selector": "input[name='username']",
    "value": "admin"
  },
  {
    "action": "type",
    "selector": "input[name='password']",
    "value": "123456"
  },
  {
    "action": "click",
    "selector": ".el-button--primary"
  },
  {
    "action": "sleep",
    "duration": 2000
  }
]
```

### 支持的操作类型

- `wait`: 等待元素出现
- `click`: 点击元素
- `type`: 输入文本
- `select`: 选择下拉选项
- `sleep`: 延迟等待

## AI 提示词模板

AutoLoginManager 使用以下提示词模板来分析登录页面：

```
你是一个网页自动化专家，需要分析登录页面的 DOM 结构，生成 Puppeteer 可执行的登录步骤。

页面信息：
- URL: {页面URL}
- 标题: {页面标题}

表单信息：
{表单DOM结构}

输入框信息：
{输入框信息}

按钮信息：
{按钮信息}

用户凭据：
- 用户名: {用户名}
- 密码: {密码}

请分析这个登录页面，生成一系列登录步骤...
```

## 环境变量

设置 GLM API Key：

```bash
export GLM_API_KEY="your-api-key-here"
```

或在代码中直接设置（不推荐用于生产环境）。

## 错误处理

AutoLoginManager 提供了完善的错误处理机制：

1. **默认登录失败** - 自动尝试配置文件登录
2. **配置登录失败** - 自动调用 AI 生成新的登录逻辑
3. **AI 登录失败** - 返回失败状态，记录详细错误信息
4. **网络错误** - 自动重试，记录错误日志

## 调试技巧

1. **启用详细输出**
   ```bash
   node bin/page-validator.js check /path/to/project --verbose
   ```

2. **查看配置文件**
   ```bash
   cat .login-config.json
   ```

3. **手动测试登录步骤**
   ```javascript
   const steps = require('./.login-config.json').steps;
   for (const step of steps) {
     await loginManager.executeLoginStep(page, step);
   }
   ```

## 注意事项

1. **安全性**: 不要在代码中硬编码密码，使用环境变量或配置文件
2. **兼容性**: 确保目标网站支持自动化操作
3. **性能**: AI 生成登录逻辑需要网络请求，可能较慢
4. **维护**: 定期检查和更新登录配置，适应网站变化

## 故障排除

### 常见问题

1. **登录表单找不到**
   - 检查页面是否完全加载
   - 确认选择器是否正确
   - 尝试增加等待时间

2. **AI 生成步骤失败**
   - 检查 API Key 是否正确
   - 确认网络连接正常
   - 查看 AI 响应格式是否正确

3. **登录后仍在登录页面**
   - 检查用户名密码是否正确
   - 确认是否有验证码或其他验证步骤
   - 查看是否有权限限制

### 日志分析

启用详细输出后，可以通过日志分析问题：

```
🔑 开始自动登录流程
   尝试默认登录逻辑...
   默认登录失败: http://localhost:3000/#/login
   尝试配置文件登录逻辑...
   配置登录成功: http://localhost:3000/#/dashboard
```
