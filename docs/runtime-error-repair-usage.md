# 运行时错误修复使用指南

## 概述

运行时错误修复功能已集成到build-fixer工具中，可以在构建成功后自动监控和修复Vue应用的运行时错误。

## 基本使用

### 1. 启用运行时错误修复

```bash
# 基本用法：启用运行时错误修复
node bin/build-fixer.js fix /path/to/project --enable-runtime-repair

# 使用你的项目路径
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus --enable-runtime-repair
```

### 2. 自定义运行时修复配置

```bash
# 完整配置示例
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --enable-runtime-repair \
  --runtime-timeout 120000 \
  --runtime-port 3001 \
  --no-runtime-auto-fix \
  --verbose
```

## 配置选项

### 命令行选项

| 选项 | 描述 | 默认值 |
|------|------|--------|
| `--enable-runtime-repair` | 启用运行时错误修复 | `false` |
| `--runtime-timeout <ms>` | 运行时监控超时时间（毫秒） | `60000` (1分钟) |
| `--runtime-port <port>` | 开发服务器端口 | `3000` |
| `--no-runtime-auto-fix` | 禁用运行时错误自动修复 | `true` (默认启用) |

### 配置文件

在项目根目录创建 `build-fixer.config.json`：

```json
{
  "buildCommand": "pnpm build",
  "devCommand": "pnpm dev",
  "installCommand": "pnpm install",
  "maxAttempts": 6,
  "mode": "build",
  "devTimeout": 30000,
  "useLegacyPeerDeps": true,
  "skipInstall": false,
  "skipAI": false,
  "dryRun": false,
  "verbose": false,
  "enableRuntimeRepair": true,
  "runtimeTimeout": 120000,
  "runtimeAutoFix": true,
  "runtimePort": 3000
}
```

## 工作流程

### 1. 标准流程

```bash
# 1. 执行构建时修复
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus

# 2. 如果构建成功且启用了运行时修复，自动执行：
#    - 启动开发服务器
#    - 注入错误监控代码
#    - 监控运行时错误
#    - AI自动修复错误
#    - 生成修复报告
```

### 2. 仅运行时修复模式

如果你的项目已经可以构建成功，只想进行运行时错误检测：

```bash
# 使用dev模式 + 运行时修复
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --mode dev \
  --enable-runtime-repair \
  --runtime-timeout 180000 \
  --verbose
```

## 使用场景

### 场景1: Vue 2到Vue 3迁移验证

```bash
# 完整的迁移验证流程
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --enable-runtime-repair \
  --runtime-timeout 300000 \
  --verbose
```

**预期输出**:
```
🔧 开始构建错误修复...
📁 项目路径: /Users/<USER>/works/galaxy/vue3-element-plus
🎯 运行模式: build
🔄 最大尝试次数: 6
🔧 运行时修复: 启用
⏱️  运行时监控: 300s
🌐 监控端口: 3000
🤖 自动修复: 启用

✅ 项目构建成功！
🚀 启动运行时错误监控...
🌐 开发服务器已启动 (端口: 3000)
👀 开始监控运行时错误 (300秒)...
✅ 运行时错误修复完成

📊 修复结果总览:
🎉 状态: 完全成功
┌─ 统计信息
├─ 构建尝试: 1 次
├─ 修复错误: 0 个
├─ 运行时修复: 成功
├─ 运行时错误: 3 个
├─ 运行时修复: 2 个
└─ 总耗时: 320s
```

### 场景2: 开发过程中的错误监控

```bash
# 快速错误检测（30秒）
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --mode dev \
  --enable-runtime-repair \
  --runtime-timeout 30000 \
  --no-runtime-auto-fix
```

### 场景3: CI/CD集成

```bash
# CI环境中的自动化检测
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --enable-runtime-repair \
  --runtime-timeout 180000 \
  --no-runtime-auto-fix \
  --verbose
```

## 错误类型支持

运行时错误修复支持以下类型的错误：

### 1. Vue响应式错误
- ref/reactive使用错误
- computed依赖问题
- watch监听器错误

### 2. 组件生命周期错误
- 生命周期钩子使用错误
- 异步操作时序问题
- 组件卸载清理问题

### 3. 模板渲染错误
- 模板语法错误
- v-if/v-for条件判断
- 数据存在性检查

### 4. 事件处理错误
- 事件处理器绑定错误
- 事件参数传递问题

## 输出解读

### 成功示例

```
📊 运行时修复统计:
  监控时长: 60秒
  检测错误: 5个
  修复错误: 4个
  修复成功率: 80.0%
```

### 失败示例

```
⚠️  运行时错误修复失败
错误: 开发服务器启动失败: EADDRINUSE
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 使用不同端口
   node bin/build-fixer.js fix /path/to/project \
     --enable-runtime-repair \
     --runtime-port 3001
   ```

2. **开发服务器启动失败**
   ```bash
   # 检查dev命令是否正确
   node bin/build-fixer.js fix /path/to/project \
     --dev-command "npm run serve" \
     --enable-runtime-repair
   ```

3. **监控超时**
   ```bash
   # 延长监控时间
   node bin/build-fixer.js fix /path/to/project \
     --enable-runtime-repair \
     --runtime-timeout 300000
   ```

### 调试技巧

```bash
# 启用详细日志
node bin/build-fixer.js fix /path/to/project \
  --enable-runtime-repair \
  --verbose

# 预览模式（不实际修改文件）
node bin/build-fixer.js fix /path/to/project \
  --enable-runtime-repair \
  --dry-run
```

## 最佳实践

### 1. 推荐配置

对于Vue 2到Vue 3迁移项目：

```bash
node bin/build-fixer.js fix /Users/<USER>/works/galaxy/vue3-element-plus \
  --enable-runtime-repair \
  --runtime-timeout 180000 \
  --verbose
```

### 2. 分阶段使用

1. **第一阶段**: 构建时修复
   ```bash
   node bin/build-fixer.js fix /path/to/project
   ```

2. **第二阶段**: 运行时验证
   ```bash
   node bin/build-fixer.js fix /path/to/project \
     --mode dev \
     --enable-runtime-repair
   ```

### 3. 团队协作

在团队中使用配置文件统一设置：

```json
{
  "enableRuntimeRepair": true,
  "runtimeTimeout": 120000,
  "runtimeAutoFix": true,
  "verbose": true
}
```

## 注意事项

1. **性能影响**: 运行时监控会启动开发服务器，消耗额外资源
2. **网络要求**: AI修复需要网络连接
3. **文件权限**: 确保有写入项目文件的权限
4. **端口冲突**: 确保指定的端口未被占用

## 更多帮助

```bash
# 查看所有选项
node bin/build-fixer.js --help

# 查看配置信息
node bin/build-fixer.js config --path /path/to/project

# 初始化配置文件
node bin/build-fixer.js init --path /path/to/project
```
