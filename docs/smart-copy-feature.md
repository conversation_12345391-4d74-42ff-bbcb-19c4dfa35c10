# 智能文件复制功能 🧠

智能文件复制是 AutoMigrator 的一个重要功能，它解决了在 Vue 2 到 Vue 3 迁移过程中可能遗漏重要文件的问题。通过结合 AI 分析和默认策略，确保项目运行所需的所有文件都能被正确复制。

## 功能概述

在 Vue 项目迁移过程中，除了核心的 `src` 目录和 `package.json` 文件，还有很多其他文件对项目的正常运行至关重要，比如：

- **mock 数据目录**：用于开发环境的模拟 API 数据
- **静态资源目录**：public/、static/等静态文件目录
- **配置文件**：vue.config.js、babel.config.js 等构建配置
- **环境配置**：.env.* 环境变量文件
- **工具脚本**：scripts/ 目录中的自定义脚本

智能文件复制功能通过两种模式来识别和复制这些文件：

## 🤖 AI 智能分析模式

当检测到有可用的 AI API Key 时，系统会使用 BuildFixAgent 来分析项目结构。

### 工作原理

1. **项目结构分析**：AI 使用 `list_files` 工具扫描项目根目录
2. **文件重要性评估**：AI 分析每个文件/目录对项目运行的重要性
3. **智能筛选**：排除不必要的文件（缓存、日志、临时文件等）
4. **精准复制**：只复制真正需要的文件和目录

### AI 分析提示词

AI 会收到专业的提示词，包含：
- 项目分析任务说明
- 常见需要复制的文件类型
- 工具使用方法
- 响应格式要求

### 支持的 AI 服务

- **DeepSeek**：通过环境变量 `DEEPSEEK_API_KEY`
- **GLM（智谱 AI）**：通过环境变量 `GLM_API_KEY`
- **OpenAI**：通过环境变量 `OPENAI_API_KEY`

### 使用示例

```javascript
const migrator = new AutoMigrator(sourceProjectPath, {
  sourceToTargetMode: true,
  sourceProjectPath: './old-project',
  targetProjectPath: './new-project',
  aiApiKey: process.env.DEEPSEEK_API_KEY,
  verbose: true
});

await migrator.smartCopyAdditionalFiles();
```

## 📁 默认策略模式

当 AI 不可用时（没有 API Key 或 AI 服务异常），系统会使用预设的默认策略。

### 默认复制的配置文件

```javascript
const defaultConfigFiles = [
  'vue.config.js',      // Vue CLI 配置
  'vite.config.js',     // Vite 配置
  'vite.config.ts',     // TypeScript Vite 配置
  'babel.config.js',    // Babel 配置
  '.eslintrc.js',       // ESLint 配置
  '.eslintrc.json',     // ESLint JSON 配置
  'tsconfig.json',      // TypeScript 配置
  'jest.config.js',     // Jest 测试配置
  '.env',               // 环境变量
  '.env.development',   // 开发环境变量
  '.env.production',    // 生产环境变量
  '.env.local',         // 本地环境变量
  '.gitignore',         // Git 忽略文件
  '.editorconfig'       // 编辑器配置
];
```

### 默认复制的目录

```javascript
const defaultDirectories = [
  'mock',               // Mock 数据目录
  'public',            // 静态资源目录
  'static',            // 静态文件目录
  'assets',            // 资源文件目录
  'scripts',           // 脚本目录
  'build',             // 构建脚本目录
  'config',            // 配置目录
  'tests',             // 测试目录
  '__tests__'          // Jest 测试目录
];
```

## 🔍 智能过滤机制

无论是 AI 模式还是默认模式，都会应用智能过滤机制：

### 排除的文件和目录

```javascript
const excludeList = [
  'src',                // 已单独处理
  'node_modules',       // 依赖包目录
  'dist',              // 构建产物
  'build',             // 构建产物（如果不是配置目录）
  '.git',              // Git 版本控制
  'package.json',      // 已单独处理
  'package-lock.json', // 锁定文件
  'yarn.lock',         // Yarn 锁定文件
  'pnpm-lock.yaml',    // PNPM 锁定文件
  '.DS_Store',         // macOS 系统文件
  'Thumbs.db'          // Windows 系统文件
];
```

### 排除的文件模式

- `*.log`：日志文件
- 以 `.` 开头的隐藏文件（除了重要配置文件）
- 临时文件和缓存文件

## 🎯 使用场景

### 源到目标迁移模式

```javascript
const migrator = new AutoMigrator(sourceProjectPath, {
  sourceToTargetMode: true,
  sourceProjectPath: './vue2-project',
  targetProjectPath: './vue3-project',
  aiApiKey: process.env.DEEPSEEK_API_KEY
});

await migrator.migrate(); // 智能复制会自动执行
```

### 新目录迁移模式

```javascript
const migrator = new AutoMigrator('./vue2-project', {
  newDirectoryMode: true,
  destinationPath: './migrated-project'
});

await migrator.migrate(); // 智能复制会自动执行
```

## 📊 监控和反馈

智能复制过程会提供详细的反馈信息：

### AI 模式反馈

```
🧠 智能分析项目结构，确定需要复制的文件...
   使用 AI 分析项目结构...
   AI 识别出 6 个需要复制的文件/目录:
   复制目录: mock
   复制文件: vue.config.js
   复制文件: .env.development
   复制目录: public
   复制文件: babel.config.js
   复制目录: scripts
```

### 默认模式反馈

```
🧠 智能分析项目结构，确定需要复制的文件...
   AI 不可用，使用默认策略复制文件...
   复制默认配置文件...
   复制配置文件: vue.config.js
   复制配置文件: .env.development
   复制默认目录...
   复制目录: mock
   复制目录: public
```

## ⚠️ 注意事项

1. **AI API Key 安全**：不要在代码中硬编码 API Key，使用环境变量
2. **网络连接**：AI 模式需要稳定的网络连接
3. **文件权限**：确保有足够的文件读写权限
4. **磁盘空间**：确保目标目录有足够的磁盘空间

## 🔧 故障排除

### AI 模式不工作

1. 检查环境变量是否正确设置
2. 检查网络连接
3. 检查 API Key 是否有效
4. 查看详细日志（使用 `verbose: true`）

### 默认模式遗漏文件

1. 检查源项目中是否确实存在相关文件
2. 查看复制过程的详细输出
3. 手动补充遗漏的特殊文件

### 文件复制失败

1. 检查文件权限
2. 检查磁盘空间
3. 查看错误日志中的具体信息

## 🚀 最佳实践

1. **优先使用 AI 模式**：为了获得最精准的文件识别
2. **开启详细输出**：使用 `verbose: true` 监控复制过程
3. **验证复制结果**：迁移完成后检查重要文件是否都存在
4. **备份重要数据**：在迁移前备份重要的项目文件
5. **测试运行**：复制完成后测试项目是否能正常运行

## 📈 未来改进

- 支持更多 AI 服务提供商
- 优化项目结构分析算法
- 支持自定义复制规则
- 增加文件完整性验证
- 支持增量复制模式

智能文件复制功能显著提高了 Vue 项目迁移的成功率和效率，确保不会因为遗漏重要文件而导致项目无法正常运行。 