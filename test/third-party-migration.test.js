const path = require('path');
const fs = require('fs-extra');
const ThirdPartyMigrationAgent = require('../src/third-party/ThirdPartyMigrationAgent');
const ComponentSearcher = require('../src/third-party/ComponentSearcher');
const MigrationRuleEngine = require('../src/third-party/MigrationRuleEngine');

describe('ThirdPartyMigrationAgent', () => {
  const testProjectPath = path.join(__dirname, 'fixtures', 'vue2-project');
  
  beforeAll(async () => {
    // 创建测试项目结构
    await fs.ensureDir(testProjectPath);
    await fs.ensureDir(path.join(testProjectPath, 'src', 'components'));
    
    // 创建package.json
    await fs.writeJson(path.join(testProjectPath, 'package.json'), {
      name: 'test-vue2-project',
      dependencies: {
        'vue': '^2.6.14',
        'vue-count-to': '^1.0.13',
        'vuedraggable': '^2.24.3',
        'vue-splitpane': '^1.0.6'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14'
      }
    });

    // 创建测试Vue文件
    await fs.writeFile(path.join(testProjectPath, 'src', 'components', 'TestComponent.vue'), `
<template>
  <div>
    <count-to :start-val="0" :end-val="100"></count-to>
    <draggable v-model="list" @start="onStart" @end="onEnd">
      <div v-for="item in list" :key="item.id">{{ item.name }}</div>
    </draggable>
    <split-pane>
      <template slot="paneL">Left pane</template>
      <template slot="paneR">Right pane</template>
    </split-pane>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import draggable from 'vuedraggable'
import splitPane from 'vue-splitpane'

export default {
  name: 'TestComponent',
  components: {
    CountTo,
    draggable,
    splitPane
  },
  data() {
    return {
      list: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' }
      ]
    }
  },
  methods: {
    onStart() {
      console.log('Drag started')
    },
    onEnd() {
      console.log('Drag ended')
    }
  }
}
</script>
`);

    // 创建JS文件
    await fs.writeFile(path.join(testProjectPath, 'src', 'utils.js'), `
const VueTemplateCompiler = require('vue-template-compiler')

function compileTemplate(template) {
  return VueTemplateCompiler.compile(template)
}

module.exports = {
  compileTemplate
}
`);
  });

  afterAll(async () => {
    // 清理测试文件
    await fs.remove(testProjectPath);
  });

  describe('ComponentSearcher', () => {
    test('should find third-party components in project', async () => {
      const searcher = new ComponentSearcher(testProjectPath, {
        useRipgrep: false, // 使用glob以确保测试稳定性
        verbose: false
      });

      const result = await searcher.searchAllComponents();

      expect(result.components.length).toBeGreaterThan(0);
      expect(result.totalFiles).toBeGreaterThan(0);
      
      const componentNames = result.components.map(c => c.name);
      expect(componentNames).toContain('vue-count-to');
      expect(componentNames).toContain('vuedraggable');
      expect(componentNames).toContain('vue-splitpane');
    });

    test('should analyze file usage correctly', async () => {
      const searcher = new ComponentSearcher(testProjectPath, {
        useRipgrep: false,
        verbose: false
      });

      const testFilePath = path.join(testProjectPath, 'src', 'components', 'TestComponent.vue');
      const fileInfo = await searcher.analyzeFileUsage(testFilePath, ['vue-count-to', 'countTo']);

      expect(fileInfo.hasUsage).toBe(true);
      expect(fileInfo.usages.length).toBeGreaterThan(0);
      expect(fileInfo.complexity).toBeDefined();
    });
  });

  describe('MigrationRuleEngine', () => {
    test('should apply import rules correctly', () => {
      const ruleEngine = new MigrationRuleEngine();
      const content = `import CountTo from 'vue-count-to'`;
      const component = { name: 'vue-count-to' };

      const result = ruleEngine.applyRules(content, component);

      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(true);
      expect(result.content).toContain('vue3-count-to');
      expect(result.appliedRules.length).toBeGreaterThan(0);
    });

    test('should handle component rules', () => {
      const ruleEngine = new MigrationRuleEngine();
      const content = `<split-pane><pane>content</pane></split-pane>`;
      const component = { name: 'vue-splitpane' };

      const result = ruleEngine.applyRules(content, component);

      expect(result.success).toBe(true);
      expect(result.hasChanges).toBe(true);
      expect(result.content).toContain('splitpanes');
    });

    test('should assess complexity correctly', () => {
      const ruleEngine = new MigrationRuleEngine();
      const simpleContent = `import CountTo from 'vue-count-to'`;
      const complexContent = `
        import CountTo from 'vue-count-to'
        export default {
          render(h) {
            return h('div', this.$slots.default)
          },
          mounted() {
            this.$refs.counter.$emit('start')
          }
        }
      `;

      const component = { name: 'vue-count-to' };

      const simpleAssessment = ruleEngine.getComplexityAssessment(simpleContent, component);
      const complexAssessment = ruleEngine.getComplexityAssessment(complexContent, component);

      expect(simpleAssessment.canUseRules).toBe(true);
      expect(simpleAssessment.recommendedApproach).toBe('rules');
      
      expect(complexAssessment.canUseRules).toBe(false);
      expect(complexAssessment.recommendedApproach).toContain('ai');
    });

    test('should validate rules correctly', () => {
      const ruleEngine = new MigrationRuleEngine();
      const validation = ruleEngine.validateRules();

      expect(validation.valid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    test('should get supported components', () => {
      const ruleEngine = new MigrationRuleEngine();
      const supported = ruleEngine.getSupportedComponents();

      expect(supported).toContain('vue-count-to');
      expect(supported).toContain('vuedraggable');
      expect(supported).toContain('vue-splitpane');
      expect(supported).toContain('vue-template-compiler');
    });

    test('should provide rule statistics', () => {
      const ruleEngine = new MigrationRuleEngine();
      const stats = ruleEngine.getRuleStats('vue-count-to');

      expect(stats).toBeDefined();
      expect(stats.component).toBe('vue-count-to');
      expect(stats.totalRules).toBeGreaterThan(0);
      expect(typeof stats.importRules).toBe('number');
      expect(typeof stats.componentRules).toBe('number');
      expect(typeof stats.apiRules).toBe('number');
    });
  });

  describe('ThirdPartyMigrationAgent Integration', () => {
    test('should create agent with correct options', () => {
      const agent = new ThirdPartyMigrationAgent(testProjectPath, {
        maxFileSize: 100,
        dryRun: true,
        verbose: false
      });

      expect(agent.projectPath).toBe(testProjectPath);
      expect(agent.options.maxFileSize).toBe(100);
      expect(agent.options.dryRun).toBe(true);
    });

    test('should analyze project correctly', async () => {
      const agent = new ThirdPartyMigrationAgent(testProjectPath, {
        dryRun: true,
        verbose: false,
        useRipgrep: false
      });

      const analysisResult = await agent.analyzeThirdPartyUsage();

      expect(analysisResult.components.length).toBeGreaterThan(0);
      expect(analysisResult.totalFiles).toBeGreaterThan(0);
    });

    // 注意：这个测试需要AI服务，在实际环境中可能需要mock
    test('should handle migration workflow', async () => {
      const agent = new ThirdPartyMigrationAgent(testProjectPath, {
        dryRun: true, // 使用dry-run避免实际修改
        verbose: false,
        useRipgrep: false,
        maxFileSize: 50 // 强制使用规则引擎
      });

      // Mock AI服务为未启用状态，这样会优先使用规则引擎
      agent.enabled = false;

      const result = await agent.migrate();

      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      expect(result.stats).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle non-existent project path', async () => {
      const nonExistentPath = path.join(__dirname, 'non-existent');
      const agent = new ThirdPartyMigrationAgent(nonExistentPath, {
        dryRun: true
      });

      const result = await agent.migrate();
      
      // 应该优雅地处理错误
      expect(result.success).toBeDefined();
    });

    test('should handle invalid file content', () => {
      const ruleEngine = new MigrationRuleEngine();
      const invalidContent = null;
      const component = { name: 'vue-count-to' };

      expect(() => {
        ruleEngine.applyRules(invalidContent, component);
      }).not.toThrow();
    });
  });
});
