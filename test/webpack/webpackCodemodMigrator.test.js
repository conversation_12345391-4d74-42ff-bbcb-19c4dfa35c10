const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const WebpackCodemodMigrator = require('../../src/webpack/WebpackCodemodMigrator');
const WebpackConfigDetector = require('../../src/webpack/utils/webpackConfigDetector');

describe('WebpackCodemodMigrator', () => {
  let tempDir;
  let migrator;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'webpack-codemod-test-'));
    migrator = new WebpackCodemodMigrator(tempDir, { 
      dryRun: false,
      verbose: true,
      backup: false // 测试时不创建备份
    });
  });

  afterEach(async () => {
    // 清理临时目录
    await fs.remove(tempDir);
  });

  describe('migrate-library-target-to-library-object', () => {
    test('should transform library target to library object', async () => {
      const webpackConfig = `
module.exports = {
  output: {
    library: 'MyLibrary',
    libraryTarget: 'commonjs2'
  }
};
`;

      const expectedConfig = `
module.exports = {
  output: {
    library: {
      name: 'MyLibrary',
      type: 'commonjs2',
    },
  },
};
`;

      // 创建测试文件
      const configPath = path.join(tempDir, 'webpack.config.js');
      await fs.writeFile(configPath, webpackConfig);

      // 执行迁移
      const result = await migrator.migrate();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.stats.modifiedFiles).toBe(1);

      // 验证文件内容
      const transformedContent = await fs.readFile(configPath, 'utf8');
      expect(transformedContent.trim()).toContain('library: {');
      expect(transformedContent.trim()).toContain('name: \'MyLibrary\'');
      expect(transformedContent.trim()).toContain('type: \'commonjs2\'');
      expect(transformedContent.trim()).not.toContain('libraryTarget');
    });

    test('should handle library without libraryTarget', async () => {
      const webpackConfig = `
module.exports = {
  output: {
    library: 'MyLibrary'
  }
};
`;

      const configPath = path.join(tempDir, 'webpack.config.js');
      await fs.writeFile(configPath, webpackConfig);

      const result = await migrator.migrate();

      expect(result.success).toBe(true);
      const transformedContent = await fs.readFile(configPath, 'utf8');
      expect(transformedContent.trim()).toContain('name: \'MyLibrary\'');
    });
  });

  describe('set-target-to-false-and-update-plugins', () => {
    test('should transform target function to plugins', async () => {
      const webpackConfig = `
const WebExtensionTarget = require('webpack-target-webextension');

module.exports = {
  target: WebExtensionTarget({
    background: {
      entry: 'background'
    }
  })
};
`;

      const configPath = path.join(tempDir, 'webpack.config.js');
      await fs.writeFile(configPath, webpackConfig);

      const result = await migrator.migrate();

      expect(result.success).toBe(true);
      const transformedContent = await fs.readFile(configPath, 'utf8');
      expect(transformedContent.trim()).toContain('target: false');
      expect(transformedContent.trim()).toContain('plugins: [');
      expect(transformedContent.trim()).toContain('WebExtensionTarget');
    });

    test('should add to existing plugins array', async () => {
      const webpackConfig = `
const WebExtensionTarget = require('webpack-target-webextension');

module.exports = {
  target: WebExtensionTarget({}),
  plugins: [
    new SomeOtherPlugin()
  ]
};
`;

      const configPath = path.join(tempDir, 'webpack.config.js');
      await fs.writeFile(configPath, webpackConfig);

      const result = await migrator.migrate();

      expect(result.success).toBe(true);
      const transformedContent = await fs.readFile(configPath, 'utf8');
      expect(transformedContent.trim()).toContain('target: false');
      expect(transformedContent.trim()).toContain('SomeOtherPlugin()');
      expect(transformedContent.trim()).toContain('WebExtensionTarget');
    });
  });

  describe('json-imports-to-default-imports', () => {
    test('should transform named JSON imports to default imports', async () => {
      const jsFile = `
import { version, name } from './package.json';

console.log(name, version);
`;

      const filePath = path.join(tempDir, 'index.js');
      await fs.writeFile(filePath, jsFile);

      const result = await migrator.migrate();

      expect(result.success).toBe(true);
      const transformedContent = await fs.readFile(filePath, 'utf8');
      expect(transformedContent.trim()).toContain('import pkg from \'./package.json\'');
      expect(transformedContent.trim()).toContain('pkg.name');
      expect(transformedContent.trim()).toContain('pkg.version');
      expect(transformedContent.trim()).not.toContain('import { version, name }');
    });

    test('should handle require with destructuring', async () => {
      const jsFile = `
const { version, name } = require('./package.json');

console.log(name, version);
`;

      const filePath = path.join(tempDir, 'index.js');
      await fs.writeFile(filePath, jsFile);

      const result = await migrator.migrate();

      expect(result.success).toBe(true);
      const transformedContent = await fs.readFile(filePath, 'utf8');

      // 检查基本转换是否成功
      expect(transformedContent.trim()).toContain('const pkg = require(\'./package.json\')');

      // 由于 jscodeshift 的复杂性，我们先检查转换是否发生
      // 如果转换成功，应该不再包含解构语法
      expect(transformedContent.trim()).not.toContain('const { version, name }');
    });
  });

  describe('WebpackConfigDetector', () => {
    test('should detect webpack configuration', async () => {
      const detector = new WebpackConfigDetector(tempDir);

      // 创建 package.json
      const packageJson = {
        dependencies: {
          webpack: '^4.46.0'
        },
        devDependencies: {
          'webpack-cli': '^4.0.0'
        }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      // 创建 webpack 配置文件
      await fs.writeFile(path.join(tempDir, 'webpack.config.js'), 'module.exports = {};');

      const result = await detector.detectWebpack();

      expect(result.hasWebpack).toBe(true);
      expect(result.version).toBe('^4.46.0');
      expect(result.configFiles).toHaveLength(1);
      expect(result.packageJsonInfo.relatedTools.webpackCli).toBe(true);
    });

    test('should detect migration needs', async () => {
      const detector = new WebpackConfigDetector(tempDir);

      // 创建需要迁移的文件
      const webpackConfig = `
module.exports = {
  output: {
    library: 'MyLib',
    libraryTarget: 'umd'
  },
  target: SomeTarget()
};
`;

      const jsFile = `
import { version } from './package.json';
console.log(version);
`;

      await fs.writeFile(path.join(tempDir, 'webpack.config.js'), webpackConfig);
      await fs.writeFile(path.join(tempDir, 'index.js'), jsFile);

      const needs = await detector.detectMigrationNeeds();

      expect(needs.libraryTarget).toBe(true);
      expect(needs.targetFunction).toBe(true);
      expect(needs.jsonImports).toBe(true);
      expect(needs.files).toHaveLength(2);
    });

    test('should provide migration strategy recommendations', async () => {
      const detector = new WebpackConfigDetector(tempDir);

      // 创建 webpack 4 项目
      const packageJson = {
        dependencies: {
          webpack: '^4.46.0'
        }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      // 创建需要迁移的文件以提高优先级
      const webpackConfig = `
module.exports = {
  output: {
    library: 'MyLib',
    libraryTarget: 'umd'
  }
};
`;
      await fs.writeFile(path.join(tempDir, 'webpack.config.js'), webpackConfig);

      const strategy = await detector.getRecommendedStrategy();

      expect(strategy.shouldMigrate).toBe(true);
      expect(strategy.priority).toBe('high');
      expect(strategy.recommendations).toContain('检测到 webpack 4，建议进行迁移');
    });
  });

  describe('integration with existing build system', () => {
    test('should work with dry run mode', async () => {
      const dryRunMigrator = new WebpackCodemodMigrator(tempDir, { 
        dryRun: true,
        verbose: false
      });

      const webpackConfig = `
module.exports = {
  output: {
    library: 'MyLibrary',
    libraryTarget: 'commonjs2'
  }
};
`;

      const configPath = path.join(tempDir, 'webpack.config.js');
      await fs.writeFile(configPath, webpackConfig);

      const result = await dryRunMigrator.migrate();

      expect(result.success).toBe(true);
      expect(result.stats.processedFiles).toBe(1);
      expect(result.stats.modifiedFiles).toBe(0); // 干运行模式不修改文件

      // 验证原文件未被修改
      const originalContent = await fs.readFile(configPath, 'utf8');
      expect(originalContent.trim()).toContain('libraryTarget: \'commonjs2\'');
    });
  });
});
