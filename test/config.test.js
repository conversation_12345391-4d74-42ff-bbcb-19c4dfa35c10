#!/usr/bin/env node

/**
 * 配置文件加载测试
 */

const fs = require('fs-extra');
const path = require('path');

const DependencyChecker = require('../src/package-json/PackageDependencyChecker');
const PackageUpgrader = require('../src/package-json/PackageUpgrader');
const BuildFixer = require('../src/build/BuildFixer');

const testProjectPath = path.join(__dirname, 'test-project');
const configPath = path.join(__dirname, '../config/package-recommend.json');

beforeAll(async () => {
  await fs.ensureDir(testProjectPath);
  const testPackageJson = {
    name: 'test-project',
    version: '1.0.0',
    dependencies: {
      'vue': '^2.6.0',
      'element-ui': '^2.15.0',
      'vue-template-compiler': '^2.6.0',
      'axios': '^0.21.0'
    },
    devDependencies: {
      '@vue/cli-service': '^4.5.0'
    }
  };
  await fs.writeJson(path.join(testProjectPath, 'package.json'), testPackageJson, { spaces: 2 });
});

afterAll(async () => {
  await fs.remove(testProjectPath);
});

describe('Config File Loading', () => {
  it('should load and validate the main config file structure', async () => {
    const config = await fs.readJson(configPath);
    expect(config).toBeDefined();
    expect(config.knownCompatible).toBeDefined();
    expect(config.knownIncompatible).toBeDefined();
    expect(config.needsUpgrade).toBeDefined();
    expect(config.systemDependencies).toBeDefined();
  });
});

describe('DependencyChecker', () => {
  it('should load config and check dependencies', async () => {
    const checker = new DependencyChecker(testProjectPath);
    await checker.loadConfig();
    expect(checker.config).toBeDefined();
    expect(checker.config.knownCompatible.vue).toBeDefined();
    expect(checker.config.knownIncompatible['element-ui']).toBeDefined();
    const result = await checker.checkCompatibility();
    expect(result).toBeDefined();
    expect(result.total).toBeGreaterThan(0);
  });
});

describe('PackageUpgrader', () => {
  let upgrader;

  beforeEach(async () => {
    upgrader = new PackageUpgrader(testProjectPath);
    await upgrader.loadConfig();
  });

  it('should load config correctly', () => {
    expect(upgrader.config).toBeDefined();
  });

  it('should get correct dependency mapping', () => {
    const mapping = upgrader.getDependencyMapping();
    expect(mapping.vue).toBeDefined();
    expect(mapping['vue-router']).toBeDefined();
  });

  it('should get correct dependencies to remove', () => {
    const toRemove = upgrader.getDependenciesToRemove();
    expect(toRemove).toContain('element-ui');
    expect(toRemove).toContain('vue-template-compiler');
  });
});

describe('BuildFixer', () => {
  it('should load config and get correct module mapping', async () => {
    const fixer = new BuildFixer(testProjectPath);
    await fixer.initialize();
    expect(fixer.options).toBeDefined();
    const moduleMapping = fixer.getModuleMapping();
    expect(moduleMapping['element-ui']).toBe('element-plus');
    expect(moduleMapping['vue-template-compiler']).toBe('@vue/compiler-sfc');
  });
});

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error(chalk.red('💥 测试执行失败:'), error);
    process.exit(1);
  });
}
