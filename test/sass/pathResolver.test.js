const path = require('path');
const fs = require('fs-extra');
const SassPathResolver = require('../../src/sass/migrators/SassPathResolver');

describe('SassPathResolver', () => {
  let tempDir;
  let resolver;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('path-resolver-');
    resolver = new SassPathResolver(tempDir, { verbose: false });
  });

  describe('初始化', () => {
    test('应该正确初始化路径解析器', () => {
      expect(resolver.projectPath).toBe(tempDir);
      expect(resolver.nodeModulesPath).toBe(path.join(tempDir, 'node_modules'));
    });

    test('应该加载 Vite 配置', async () => {
      // 创建 Vite 配置文件
      const viteConfig = `import { defineConfig } from 'vite';
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@styles': path.resolve(__dirname, 'src/styles')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        loadPaths: [path.resolve(__dirname, 'node_modules')]
      }
    }
  }
});`;

      await fs.writeFile(path.join(tempDir, 'vite.config.js'), viteConfig);
      await resolver.initialize();

      expect(resolver.viteConfig).toBeDefined();
      expect(resolver.viteConfig.resolve.alias).toBeDefined();
    });

    test('应该加载 package.json', async () => {
      const packageJson = {
        name: 'test-project',
        dependencies: {
          'element-ui': '^2.15.0'
        }
      };

      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);
      await resolver.initialize();

      expect(resolver.packageJson).toEqual(packageJson);
    });
  });

  describe('路径映射', () => {
    beforeEach(async () => {
      await resolver.initialize();
    });

    test('应该构建默认路径映射', () => {
      expect(resolver.pathMappings.get('@')).toBe(path.join(tempDir, 'src'));
      expect(resolver.pathMappings.get('~')).toBe(resolver.nodeModulesPath);
    });

    test('应该处理 Vite 别名配置', async () => {
      const viteConfig = `export default {
        resolve: {
          alias: {
            '@components': './src/components',
            '@utils': './src/utils'
          }
        }
      };`;

      await fs.writeFile(path.join(tempDir, 'vite.config.js'), viteConfig);
      await resolver.loadViteConfig();
      resolver.buildPathMappings();

      expect(resolver.pathMappings.has('@components')).toBe(true);
      expect(resolver.pathMappings.has('@utils')).toBe(true);
    });
  });

  describe('~ 别名解析', () => {
    test('应该正确解析 ~ 别名', () => {
      const result = resolver.resolveTildeAlias('~element-ui/packages/theme-chalk/src/index');
      const expected = path.join(resolver.nodeModulesPath, 'element-ui/packages/theme-chalk/src/index');
      expect(result).toBe(expected);
    });

    test('应该保持非 ~ 路径不变', () => {
      const inputPath = './relative/path';
      const result = resolver.resolveTildeAlias(inputPath);
      expect(result).toBe(inputPath);
    });

    test('应该处理仅有 ~ 的路径', () => {
      const result = resolver.resolveTildeAlias('~');
      expect(result).toBe(resolver.nodeModulesPath);
    });
  });

  describe('别名解析', () => {
    beforeEach(async () => {
      await resolver.initialize();
    });

    test('应该解析 @ 别名', () => {
      const result = resolver.resolveAlias('@/components/Button');
      const expected = path.join(tempDir, 'src/components/Button');
      expect(result).toBe(expected);
    });

    test('应该解析自定义别名', () => {
      resolver.pathMappings.set('@components', path.join(tempDir, 'src/components'));

      const result = resolver.resolveAlias('@components/Button');
      const expected = path.join(tempDir, 'src/components/Button');
      expect(result).toBe(expected);
    });

    test('应该保持无匹配别名的路径不变', () => {
      const inputPath = 'some/random/path';
      const result = resolver.resolveAlias(inputPath);
      expect(result).toBe(inputPath);
    });
  });

  describe('库迁移', () => {
    test('应该迁移 Element UI 路径', () => {
      const oldPath = 'element-ui/packages/theme-chalk/src/index';
      const result = resolver.resolveLibraryMigration(oldPath);
      expect(result).toBe('element-plus/theme-chalk/src/index.scss');
    });

    test('应该迁移 Element UI CSS 路径', () => {
      const oldPath = 'element-ui/lib/theme-chalk/index.css';
      const result = resolver.resolveLibraryMigration(oldPath);
      expect(result).toBe('element-plus/dist/index.css');
    });

    test('应该保持非库路径不变', () => {
      const inputPath = 'custom/styles/main';
      const result = resolver.resolveLibraryMigration(inputPath);
      expect(result).toBe(inputPath);
    });
  });

  describe('相对路径解析', () => {
    test('应该解析相对路径', () => {
      const currentFile = path.join(tempDir, 'src/components/Button.vue');
      const importPath = '../styles/variables';

      const result = resolver.resolveRelativePath(importPath, currentFile);
      const expected = path.relative(tempDir, path.resolve(path.dirname(currentFile), importPath));
      expect(result).toBe(expected);
    });

    test('应该处理当前目录路径', () => {
      const currentFile = path.join(tempDir, 'src/styles/main.scss');
      const importPath = './variables';

      const result = resolver.resolveRelativePath(importPath, currentFile);
      const expected = path.relative(tempDir, path.resolve(path.dirname(currentFile), importPath));
      expect(result).toBe(expected);
    });

    test('应该在没有当前文件时返回原路径', () => {
      const importPath = '../styles/variables';
      const result = resolver.resolveRelativePath(importPath, null);
      expect(result).toBe(importPath);
    });
  });

  describe('文件扩展名处理', () => {
    test('应该为无扩展名的路径添加 .scss', () => {
      const result = resolver.ensureExtension('styles/variables');
      expect(result).toBe('styles/variables.scss');
    });

    test('应该保持已有扩展名的路径不变', () => {
      const inputPath = 'styles/variables.scss';
      const result = resolver.ensureExtension(inputPath);
      expect(result).toBe(inputPath);
    });

    test('应该保持目录路径不变', () => {
      const inputPath = 'styles/';
      const result = resolver.ensureExtension(inputPath);
      expect(result).toBe(inputPath);
    });
  });

  describe('@import 到 @use 转换', () => {
    beforeEach(async () => {
      await resolver.initialize();
    });

    test('应该转换基本的 @import 语句', () => {
      const importStatement = '@import "variables";';
      const currentFile = path.join(tempDir, 'src/styles/main.scss');

      const result = resolver.convertImportToUse(importStatement, currentFile);
      expect(result).toMatch(/@use ['"].*variables\.scss['"] as \*;/);
    });

    test('应该转换带路径的 @import 语句', () => {
      const importStatement = '@import "./utils/variables";';
      const currentFile = path.join(tempDir, 'src/styles/main.scss');

      const result = resolver.convertImportToUse(importStatement, currentFile);
      expect(result).toMatch(/@use ['"].*variables\.scss['"] as \*;/);
    });

    test('应该转换 Element UI 导入', () => {
      const importStatement = '@import "~element-ui/packages/theme-chalk/src/index";';
      const currentFile = path.join(tempDir, 'src/styles/main.scss');

      const result = resolver.convertImportToUse(importStatement, currentFile);
      expect(result).toMatch(/@use ['"]element-plus\/theme-chalk\/src\/index\.scss['"] as \*;/);
    });

    test('应该保持非 @import 语句不变', () => {
      const statement = '.button { color: red; }';
      const result = resolver.convertImportToUse(statement, null);
      expect(result).toBe(statement);
    });
  });

  describe('命名空间生成', () => {
    test('应该生成有效的命名空间', () => {
      const result = resolver.generateNamespace('src/styles/variables.scss');
      expect(result).toBe('variables');
    });

    test('应该移除下划线前缀', () => {
      const result = resolver.generateNamespace('src/styles/_variables.scss');
      expect(result).toBe('variables');
    });

    test('应该处理特殊字符', () => {
      const result = resolver.generateNamespace('src/styles/<EMAIL>');
      expect(result).toBe('my-variables-2');
    });
  });

  describe('全局访问判断', () => {
    test('应该识别内部工具文件', () => {
      expect(resolver.shouldUseGlobalAccess('src/styles/variables')).toBe(true);
      expect(resolver.shouldUseGlobalAccess('utils/mixins')).toBe(true);
      expect(resolver.shouldUseGlobalAccess('functions')).toBe(true);
    });

    test('应该识别外部库文件', () => {
      expect(resolver.shouldUseGlobalAccess('element-plus/theme-chalk')).toBe(true);
      expect(resolver.shouldUseGlobalAccess('bootstrap/scss')).toBe(false);
    });
  });

  describe('完整路径解析', () => {
    beforeEach(async () => {
      await resolver.initialize();
    });

    test('应该处理复杂的路径解析场景', () => {
      const currentFile = path.join(tempDir, 'src/components/Button.vue');
      const importPath = '~element-ui/packages/theme-chalk/src/button';

      const result = resolver.resolvePath(importPath, currentFile);
      expect(result).toBe('element-plus/theme-chalk/src/button.scss');
    });

    test('应该处理别名和相对路径组合', () => {
      const currentFile = path.join(tempDir, 'src/components/Button.vue');
      const importPath = '../styles/variables';

      const result = resolver.resolvePath(importPath, currentFile);
      const expected = path.relative(tempDir, path.resolve(path.dirname(currentFile), importPath)) + '.scss';
      expect(result).toBe(expected);
    });
  });

  describe('Vite 配置建议', () => {
    test('应该生成推荐的 Vite 配置', () => {
      const config = resolver.getRecommendedViteConfig();

      expect(config.css.preprocessorOptions.scss.loadPaths).toContain(resolver.nodeModulesPath);
      expect(config.resolve.alias['@']).toBe(path.join(tempDir, 'src'));
    });
  });
});
