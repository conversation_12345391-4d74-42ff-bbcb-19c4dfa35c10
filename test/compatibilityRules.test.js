const { CompatibilityRuleManager } = require('../src/compat/CompatRules')

describe('CompatibilityRuleManager', () => {
	let ruleManager

	beforeEach(() => {
		ruleManager = new CompatibilityRuleManager()
	})

	describe('checkCompatibility', () => {
		it('should detect html-webpack-plugin version issues', () => {
			const dependencies = {
				'html-webpack-plugin': '^4.5.0'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(1)
			expect(issues[0]).toMatchObject({
				type: 'critical',
				package: 'html-webpack-plugin',
				currentVersion: '^4.5.0',
				requiredVersion: '^5.6.0',
				error: 'htmlWebpackPlugin.getHooks is not a function'
			})
		})

		it('should detect svg-sprite-loader version issues', () => {
			const dependencies = {
				'svg-sprite-loader': '^4.1.3'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(1)
			expect(issues[0]).toMatchObject({
				type: 'critical',
				package: 'svg-sprite-loader',
				currentVersion: '^4.1.3',
				requiredVersion: '^6.0.11',
				error: 'Cannot find module \'webpack/lib/RuleSet\''
			})
		})

		it('should detect script-ext-html-webpack-plugin incompatibility', () => {
			const dependencies = {
				'script-ext-html-webpack-plugin': '^2.1.5'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(1)
			expect(issues[0]).toMatchObject({
				type: 'incompatible',
				package: 'script-ext-html-webpack-plugin',
				error: 'Incompatible with html-webpack-plugin 5.x'
			})
		})

		it('should detect multiple issues', () => {
			const dependencies = {
				'html-webpack-plugin': '^4.5.0',
				'svg-sprite-loader': '^4.1.3',
				'script-ext-html-webpack-plugin': '^2.1.5'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(3)
			expect(issues.some(issue => issue.package === 'html-webpack-plugin')).toBe(true)
			expect(issues.some(issue => issue.package === 'svg-sprite-loader')).toBe(true)
			expect(issues.some(issue => issue.package === 'script-ext-html-webpack-plugin')).toBe(true)
		})

		it('should not detect issues with compatible versions', () => {
			const dependencies = {
				'html-webpack-plugin': '^5.6.0',
				'svg-sprite-loader': '^6.0.11'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(0)
		})
	})

	describe('autoFixIssues', () => {
		it('should auto-fix critical issues', async () => {
			const packageJson = {
				devDependencies: {
					'html-webpack-plugin': '^4.5.0',
					'svg-sprite-loader': '^4.1.3'
				}
			}

			const issues = [
				{
					rule: 'html-webpack-plugin-version',
					package: 'html-webpack-plugin',
					requiredVersion: '^5.6.0'
				},
				{
					rule: 'svg-sprite-loader-version',
					package: 'svg-sprite-loader',
					requiredVersion: '^6.0.11'
				}
			]

			const modified = await ruleManager.autoFixIssues(packageJson, issues)

			expect(modified).toBe(true)
			expect(packageJson.devDependencies['html-webpack-plugin']).toBe('^5.6.0')
			expect(packageJson.devDependencies['svg-sprite-loader']).toBe('^6.0.11')
		})
	})

	describe('generateConfigSuggestions', () => {
		it('should generate config suggestions for incompatible packages', () => {
			const issues = [
				{
					rule: 'script-ext-html-webpack-plugin-incompatible',
					package: 'script-ext-html-webpack-plugin',
					error: 'Incompatible with html-webpack-plugin 5.x',
					solution: 'Remove this plugin and update vue.config.js configuration'
				}
			]

			const suggestions = ruleManager.generateConfigSuggestions(issues)

			expect(suggestions).toHaveLength(1)
			expect(suggestions[0]).toMatchObject({
				file: 'vue.config.js',
				action: 'remove',
				package: 'script-ext-html-webpack-plugin'
			})
		})
	})

	describe('addRule', () => {
		it('should allow adding custom rules', () => {
			ruleManager.addRule('custom-rule', {
				type: 'warning',
				package: 'custom-package',
				condition: (deps) => deps['custom-package'] !== undefined,
				error: 'Custom error message',
				solution: 'Custom solution'
			})

			const dependencies = {
				'custom-package': '^1.0.0'
			}

			const issues = ruleManager.checkCompatibility(dependencies)

			expect(issues).toHaveLength(1)
			expect(issues[0]).toMatchObject({
				type: 'warning',
				package: 'custom-package',
				error: 'Custom error message'
			})
		})
	})
})
