const fs = require('fs-extra');
const path = require('path');
const RouteParser = require('../src/runtime-validation/RouteParser');
const PageValidator = require('../src/runtime-validation/PageValidator');
const RuntimePageChecker = require('../src/runtime-validation/RuntimePageChecker');

describe('Runtime Validation', () => {
  let tempDir;

  beforeEach(async () => {
    tempDir = await fs.mkdtemp(path.join(__dirname, 'temp-'));
  });

  afterEach(async () => {
    if (tempDir && await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('RouteParser', () => {
    test('应该解析 Vue 3 路由配置', async () => {
      // 创建测试路由文件
      const routerContent = `
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import About from '@/views/About.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: 'Home' }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: { title: 'About' }
  },
  {
    path: '/user/:id',
    name: 'User',
    component: () => import('@/views/User.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
`;

      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), routerContent);

      const parser = new RouteParser(tempDir, { verbose: false, useAI: false });
      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      expect(result.routes.length).toBeGreaterThan(0);

      const routePaths = parser.getRoutePaths();
      expect(routePaths).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ path: '/', name: 'Home' }),
          expect.objectContaining({ path: '/about', name: 'About' }),
          expect.objectContaining({ path: '/user/:id', name: 'User' })
        ])
      );
    });

    test('应该解析 Vue 2 路由配置', async () => {
      const routerContent = `
import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
`;

      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), routerContent);

      const parser = new RouteParser(tempDir, { verbose: false, useAI: false });
      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      
      const routePaths = parser.getRoutePaths();
      expect(routePaths).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ path: '/', name: 'Home' }),
          expect.objectContaining({ path: '/about', name: 'About' })
        ])
      );
    });

    test('应该处理嵌套路由', async () => {
      const routerContent = `
const routes = [
  {
    path: '/admin',
    name: 'Admin',
    component: AdminLayout,
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: Dashboard
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: Users
      }
    ]
  }
]
`;

      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), routerContent);

      const parser = new RouteParser(tempDir, { verbose: false, useAI: false });
      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      
      const routePaths = parser.getRoutePaths();
      expect(routePaths).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ path: '/admin' }),
          expect.objectContaining({ path: '/admin/dashboard' }),
          expect.objectContaining({ path: '/admin/users' })
        ])
      );
    });

    test('应该跳过通配符路由', async () => {
      const routerContent = `
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '*',
    redirect: '/404'
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]
`;

      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), routerContent);

      const parser = new RouteParser(tempDir, { verbose: false, useAI: false });
      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      
      const routePaths = parser.getRoutePaths();
      expect(routePaths).toHaveLength(1);
      expect(routePaths[0].path).toBe('/');
    });

    test('应该处理解析错误', async () => {
      // 创建无效的路由文件
      const invalidContent = `
this is not valid javascript code
`;

      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), invalidContent);

      const parser = new RouteParser(tempDir, { verbose: false, useAI: false });
      const result = await parser.parseRoutes();

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('PageValidator', () => {
    test('应该创建 PageValidator 实例', () => {
      const routes = [
        { path: '/', name: 'Home' },
        { path: '/about', name: 'About' }
      ];

      const validator = new PageValidator(tempDir, routes, {
        baseUrl: 'http://localhost:3000',
        headless: true
      });

      expect(validator).toBeDefined();
      expect(validator.routes).toEqual(routes);
      expect(validator.baseUrl).toBe('http://localhost:3000');
    });

    test('应该检测开发命令', () => {
      const routes = [{ path: '/', name: 'Home' }];
      const validator = new PageValidator(tempDir, routes);
      
      expect(validator.options.devCommand).toBeDefined();
    });

    test('应该生成 Markdown 报告', () => {
      const routes = [{ path: '/', name: 'Home' }];
      const validator = new PageValidator(tempDir, routes);
      
      const mockReport = {
        summary: {
          total: 2,
          successful: 1,
          failed: 1,
          successRate: '50.00'
        },
        failedPages: [
          {
            route: { path: '/error' },
            url: 'http://localhost:3000/error',
            loadTime: 1000,
            errors: ['Console Error: Test error'],
            warnings: ['Test warning']
          }
        ],
        results: [
          { route: { path: '/' }, success: true, loadTime: 500 },
          { route: { path: '/error' }, success: false, loadTime: 1000 }
        ],
        timestamp: '2023-01-01T00:00:00.000Z'
      };

      const markdown = validator.generateMarkdownReport(mockReport);
      
      expect(markdown).toContain('# 页面验证报告');
      expect(markdown).toContain('总页面数: 2');
      expect(markdown).toContain('成功: 1');
      expect(markdown).toContain('失败: 1');
      expect(markdown).toContain('成功率: 50.00%');
      expect(markdown).toContain('## 失败的页面');
      expect(markdown).toContain('/error');
    });
  });

  describe('RuntimePageChecker', () => {
    test('应该创建 RuntimePageChecker 实例', () => {
      const checker = new RuntimePageChecker(tempDir, {
        verbose: false,
        autoFix: false
      });

      expect(checker).toBeDefined();
      expect(checker.projectPath).toBe(tempDir);
      expect(checker.options.verbose).toBe(false);
      expect(checker.options.autoFix).toBe(false);
    });

    test('应该检测开发命令', async () => {
      // 创建 package.json
      const packageJson = {
        name: 'test-project',
        scripts: {
          dev: 'vite',
          serve: 'vue-cli-service serve'
        }
      };

      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      const checker = new RuntimePageChecker(tempDir);
      expect(checker.options.devCommand).toBe('npm run dev');
    });

    test('应该生成建议', () => {
      const checker = new RuntimePageChecker(tempDir);
      
      // 模拟验证结果
      checker.checkResults.validation = {
        report: {
          summary: {
            total: 10,
            successful: 8,
            failed: 2,
            successRate: '80.00'
          },
          failedPages: [
            {
              errors: ['Console Error: Test error', 'Network Error: Failed to load']
            },
            {
              errors: ['Console Error: Another error']
            }
          ]
        }
      };

      const recommendations = checker.generateRecommendations();
      
      expect(recommendations).toContain('大部分页面运行正常');
      expect(recommendations).toContain('修复 JavaScript 控制台错误');
      expect(recommendations).toContain('检查网络请求和资源加载问题');
    });

    test('应该获取最常见错误', () => {
      const checker = new RuntimePageChecker(tempDir);
      
      const failedPages = [
        { errors: ['Console Error: Test error', 'Network Error: Failed'] },
        { errors: ['Console Error: Another error', 'HTTP 404: Not found'] },
        { errors: ['Console Error: Third error'] }
      ];

      const topErrors = checker.getTopErrors(failedPages);
      
      expect(topErrors).toEqual([
        ['Console Error', 3],
        ['Network Error', 1],
        ['HTTP 404', 1]
      ]);
    });
  });

  describe('Integration Tests', () => {
    test('应该完成完整的路由解析流程', async () => {
      // 创建完整的测试项目结构
      await fs.ensureDir(path.join(tempDir, 'src/router'));
      await fs.ensureDir(path.join(tempDir, 'src/views'));

      // 创建 package.json
      const packageJson = {
        name: 'test-project',
        dependencies: { vue: '^3.0.0' },
        scripts: { dev: 'vite' }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      // 创建路由文件
      const routerContent = `
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  { path: '/', name: 'Home', component: () => import('@/views/Home.vue') },
  { path: '/about', name: 'About', component: () => import('@/views/About.vue') }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
`;
      await fs.writeFile(path.join(tempDir, 'src/router/index.js'), routerContent);

      const checker = new RuntimePageChecker(tempDir, {
        verbose: false,
        useAI: false
      });

      const routeResult = await checker.parseRoutes();
      
      expect(routeResult.success).toBe(true);
      expect(routeResult.routes.length).toBe(2);
      expect(routeResult.routes).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ path: '/', name: 'Home' }),
          expect.objectContaining({ path: '/about', name: 'About' })
        ])
      );
    });
  });
});
