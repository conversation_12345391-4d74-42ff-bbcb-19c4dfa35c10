const fs = require('fs-extra');
const path = require('path');
const PackageJsonManager = require('../src/package-json/PackageJsonManager');

describe('PackageJsonManager', () => {
  let tempDir;
  let sourceDir;
  let targetDir;
  let workingDir;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, 'temp', 'package-json-manager');
    sourceDir = path.join(tempDir, 'source');
    targetDir = path.join(tempDir, 'target');
    workingDir = path.join(tempDir, 'working');

    await fs.ensureDir(sourceDir);
    await fs.ensureDir(targetDir);
    await fs.ensureDir(workingDir);

    // 创建测试用的 package.json 文件
    await createTestPackageJsonFiles();
  });

  afterEach(async () => {
    // 清理临时文件
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  async function createTestPackageJsonFiles() {
    // 源项目 package.json (Vue 2 项目)
    const sourcePackageJson = {
      name: 'vue2-source-project',
      version: '1.0.0',
      dependencies: {
        vue: '^2.6.14',
        'vue-router': '^3.5.4',
        'element-ui': '^2.15.13',
        'vue-count-to': '^1.0.13',
        axios: '^0.27.2'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14',
        '@vue/cli-service': '^4.5.15'
      }
    };

    // 目标项目 package.json (Vue 3 项目模板)
    const targetPackageJson = {
      name: 'vue3-target-project',
      version: '2.0.0',
      dependencies: {
        vue: '^3.4.0',
        'vue-router': '^4.5.0',
        'element-plus': '^2.9.0'
      },
      devDependencies: {
        '@vue/cli-service': '^5.0.8',
        vite: '^4.5.0'
      }
    };

    // 工作目录 package.json (单项目模式)
    const workingPackageJson = {
      name: 'vue-working-project',
      version: '1.0.0',
      dependencies: {
        vue: '^2.6.14',
        'vue-router': '^3.5.4',
        'element-ui': '^2.15.13'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14'
      }
    };

    await fs.writeJson(path.join(sourceDir, 'package.json'), sourcePackageJson, { spaces: 2 });
    await fs.writeJson(path.join(targetDir, 'package.json'), targetPackageJson, { spaces: 2 });
    await fs.writeJson(path.join(workingDir, 'package.json'), workingPackageJson, { spaces: 2 });
  }

  describe('基本功能测试', () => {
    it('应该能够创建 PackageJsonManager 实例', () => {
      const manager = new PackageJsonManager({
        workingPath: workingDir,
        sourceToTargetMode: false,
        dryRun: true
      });

      expect(manager).toBeDefined();
      expect(manager.options.workingPath).toBe(workingDir);
    });

    it('应该能够验证 package.json', async () => {
      const manager = new PackageJsonManager();
      const packageJsonPath = path.join(targetDir, 'package.json');
      const validation = await manager.validatePackageJson(packageJsonPath);

      expect(validation).toHaveProperty('valid');
      expect(validation).toHaveProperty('issues');
      expect(validation).toHaveProperty('packageJson');
      expect(Array.isArray(validation.issues)).toBe(true);
    });

    it('应该能够生成迁移报告', () => {
      const manager = new PackageJsonManager();
      const mockResults = {
        totalChanges: 5,
        merge: { changes: ['test change'] },
        upgrade: { upgraded: [], added: [] },
        mapping: { updated: 0, dependencies: [] }
      };

      const report = manager.generateMigrationReport(mockResults);

      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('details');
      expect(report.summary.totalChanges).toBe(5);
    });
  });
});
