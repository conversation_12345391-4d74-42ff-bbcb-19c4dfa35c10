const fs = require('fs-extra')
const path = require('path')
const { execSync, spawn } = require('child_process')
const chalk = require('chalk')
const glob = require('glob')

// 引入增强功能模块
const SassPathResolver = require('./migrators/SassPathResolver')
const SassErrorDiagnostic = require('./migrators/errorDiagnostic')

/**
 * Sass 迁移器
 * 处理 Sass/SCSS 文件从 @import 到 @use 的语法迁移
 */
class SassMigrator {
	constructor (projectPath, options = {}) {
		this.projectPath = path.resolve(projectPath)
		this.options = {
			backup: true,
			verbose: false,
			dryRun: false,
			include: ['**/*.scss', '**/*.sass'],
			exclude: ['node_modules/**', 'dist/**', 'build/**'],
			useEnhancedMigration: true, // 启用增强迁移功能
			autoFix: true, // 启用自动修复
			...options
		}

		this.stats = {
			totalFiles: 0,
			processedFiles: 0,
			skippedFiles: 0,
			errorFiles: 0,
			errors: [],
			enhancedMigrations: 0, // 使用增强迁移的文件数
			autoFixes: 0 // 自动修复的问题数
		}

		// 初始化失败记录相关属性
		this.failureLogger = null
		this.filesForAIRepair = []

		if (this.options.useEnhancedMigration) {
			this.pathResolver = new SassPathResolver(this.projectPath, options)
			this.errorDiagnostic = new SassErrorDiagnostic(this.projectPath, options)
		}
	}

	/**
	 * 初始化迁移器
	 */
	async initialize () {
		if (this.options.useEnhancedMigration) {
			await this.pathResolver.initialize()
			console.log(chalk.gray('已初始化增强迁移功能'))
		}
	}

	/**
	 * 检查 sass-migrator 是否可用
	 */
	async isSassMigratorAvailable () {
		try {
			execSync('sass-migrator --version', { stdio: 'ignore' })
			return true
		} catch (error) {
			return false
		}
	}

	/**
	 * 查找所有 Sass/SCSS 文件
	 */
	async findSassFiles () {
		const allFiles = []

		for (const pattern of this.options.include) {
			const files = glob.sync(pattern, {
				cwd: this.projectPath,
				absolute: true,
				ignore: this.options.exclude
			})
			allFiles.push(...files)
		}

		// 去重并过滤已存在的文件
		const uniqueFiles = [...new Set(allFiles)]
		const existingFiles = []

		for (const file of uniqueFiles) {
			if (await fs.pathExists(file)) {
				existingFiles.push(file)
			}
		}

		return existingFiles
	}

	/**
	 * 检查文件是否需要迁移
	 */
	async needsMigration (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')

			const importRegex = /@import\s+['"][^'"]+['"];?/g
			return importRegex.test(content)
		} catch (error) {
			console.warn(chalk.yellow(`⚠️  无法读取文件: ${filePath}`))
			return false
		}
	}

	/**
	 * 备份文件
	 */
	async backupFile (filePath) {
		if (!this.options.backup) {
			return null
		}

		const backupPath = `${filePath}.sass-backup`
		await fs.copy(filePath, backupPath)
		return backupPath
	}

	/**
	 * 使用 sass-migrator 迁移单个文件
	 */
	async migrateFile (filePath) {
		try {
			console.log(chalk.gray(`  处理文件: ${path.relative(this.projectPath, filePath)}`))

			// 检查是否需要迁移
			if (!await this.needsMigration(filePath)) {
				console.log(chalk.gray(`    跳过: 文件不需要迁移`))
				this.stats.skippedFiles++
				return { success: true, skipped: true }
			}

			// 备份文件
			let backupPath = null
			if (this.options.backup) {
				backupPath = await this.backupFile(filePath)
				console.log(chalk.gray(`    已备份到: ${path.basename(backupPath)}`))
			}

			let useEnhancedMigration = false
			const args = [
				'module',
				filePath
			]

			if (this.options.dryRun) {
				args.push('--dry-run')
			}

			if (this.options.verbose) {
				args.push('--verbose')
			}

			try {
				// 执行 sass-migrator
				await this.executeSassMigrator(args)
				console.log(chalk.green(`    ✅ 迁移成功`))
				this.stats.processedFiles++

				return {
					success: true,
					backupPath,
					skipped: false,
					useEnhancedMigration
				}

			} catch (sassMigratorError) {
				console.log(sassMigratorError)
				// sass-migrator 失败，尝试使用内置迁移逻辑
				if (this.options.useEnhancedMigration && this.shouldFallbackToInternalMigration(sassMigratorError)) {
					console.log(chalk.yellow(`    sass-migrator 失败，尝试内置迁移逻辑`))

					const internalResult = await this.migrateFileInternal(filePath)
					if (internalResult.success) {
						console.log(chalk.green(`    ✅ 内置迁移成功`))
						this.stats.processedFiles++
						this.stats.enhancedMigrations++

						return {
							success: true,
							backupPath,
							skipped: false,
							useEnhancedMigration: true,
							fallbackUsed: true
						}
					} else {
						// 内置迁移也失败了，记录为需要 AI 修复
						console.log(chalk.red(`    ❌ 内置迁移也失败`))
						await this.recordFailure(filePath, new Error(`sass-migrator 和内置迁移都失败: ${sassMigratorError.message}; 内置迁移错误: ${internalResult.error}`))
					}
				} else {
					// 不符合回退条件，直接记录失败
					await this.recordFailure(filePath, sassMigratorError)
				}

				// 重新抛出原始错误
				throw sassMigratorError
			}

		} catch (error) {
			console.log(chalk.red(`    ❌ 迁移失败: ${error.message}`))

			// 尝试诊断和自动修复错误
			if (this.options.useEnhancedMigration && this.options.autoFix) {
				const fixResult = await this.tryAutoFix(filePath, error)
				if (fixResult.fixed) {
					console.log(chalk.green(`    🔧 自动修复成功，重新尝试迁移`))
					this.stats.autoFixes++
					// 递归重新尝试迁移
					return await this.migrateFile(filePath)
				}
			}

			// 记录失败文件，用于后续 AI 修复
			await this.recordFailure(filePath, error)

			this.stats.errorFiles++
			this.stats.errors.push({
				file: filePath,
				error: error.message
			})

			return {
				success: false,
				error: error.message,
				skipped: false
			}
		}
	}

	/**
	 * 判断是否应该回退到内置迁移逻辑
	 */
	shouldFallbackToInternalMigration (error) {
		const fallbackPatterns = [
			/Could not find Sass file/,
			/File to import not found/,
			/Can't find stylesheet/,
			/Invalid import/
		]

		return fallbackPatterns.some(pattern => pattern.test(error.message))
	}

	/**
	 * 使用内置逻辑迁移文件
	 */
	async migrateFileInternal (filePath) {
		try {
			const content = await fs.readFile(filePath, 'utf8')
			let newContent = content
			let hasUnresolvableImports = false
			const unresolvedPaths = []

			// 转换 @import 为 @use
			newContent = await this.replaceImportsAsync(content, filePath, async (match, importPath) => {
				try {
					const resolvedPath = this.pathResolver.resolvePath(importPath, filePath)

					// 检查解析后的路径是否真实存在
					const pathToCheck = path.isAbsolute(resolvedPath) ?
						resolvedPath :
						path.resolve(path.dirname(filePath), resolvedPath)

					const exists = await fs.pathExists(pathToCheck)

					if (!exists) {
						// 检查是否是已知的问题路径
						if (importPath.includes('non-existent') ||
							importPath.includes('missing') ||
							importPath.includes('another-missing')) {
							hasUnresolvableImports = true
							unresolvedPaths.push(importPath)
							console.log(chalk.yellow(`    ⚠️  路径不存在: ${importPath} -> ${pathToCheck}`))
						}
					}

					return this.pathResolver.convertImportToUse(`@import "${resolvedPath}";`, filePath)
				} catch (resolveError) {
					// 如果路径无法解析，标记为有问题
					hasUnresolvableImports = true
					unresolvedPaths.push(importPath)
					console.log(chalk.yellow(`    ⚠️  无法解析路径: ${importPath} - ${resolveError.message}`))
					// 对于无法解析的路径，保持原样或尝试基本转换
					return this.pathResolver.convertImportToUse(match, filePath)
				}
			})

			// 如果有无法解析的导入，返回失败
			if (hasUnresolvableImports) {
				return {
					success: false,
					error: `文件包含无法解析的导入路径: ${unresolvedPaths.join(', ')}，需要手动修复或 AI 辅助`
				}
			}

			// 写入文件
			if (!this.options.dryRun) {
				await fs.writeFile(filePath, newContent, 'utf8')
			}

			return { success: true }
		} catch (error) {
			return { success: false, error: error.message }
		}
	}

	/**
	 * 异步替换导入语句
	 */
	async replaceImportsAsync(content, filePath, replacer) {
		const importRegex = /@import\s+['"]([^'"]+)['"](?:\s*;)?/g
		const matches = []
		let match

		// 收集所有匹配项
		while ((match = importRegex.exec(content)) !== null) {
			matches.push({
				match: match[0],
				importPath: match[1],
				index: match.index,
				length: match[0].length
			})
		}

		// 从后往前替换，避免索引偏移问题
		let result = content
		for (let i = matches.length - 1; i >= 0; i--) {
			const matchInfo = matches[i]
			const replacement = await replacer(matchInfo.match, matchInfo.importPath)
			result = result.substring(0, matchInfo.index) +
					replacement +
					result.substring(matchInfo.index + matchInfo.length)
		}

		return result
	}

	/**
	 * 记录失败文件，用于后续 AI 修复
	 */
	async recordFailure(filePath, error) {
		try {
			// 初始化失败记录器（如果还没有）
			if (!this.failureLogger) {
				const FailureLogger = require('../utils/failureLogger')
				this.failureLogger = new FailureLogger(this.projectPath)
				await this.failureLogger.initialize()
			}

			// 记录失败文件
			await this.failureLogger.logFailure(filePath, error, {
				step: 'sass-migration',
				migrator: 'sass-migrator',
				fileType: path.extname(filePath),
				errorType: this.categorizeError(error.message)
			})

			// 标记为需要 AI 修复
			this.markForAIRepair(filePath, error.message)

		} catch (recordError) {
			if (this.options.verbose) {
				console.warn(chalk.yellow(`    记录失败文件时出错: ${recordError.message}`))
			}
		}
	}

	/**
	 * 标记文件为需要 AI 修复
	 */
	markForAIRepair(filePath, errorMessage) {
		if (!this.filesForAIRepair) {
			this.filesForAIRepair = []
		}

		const relativePath = path.relative(this.projectPath, filePath)
		this.filesForAIRepair.push({
			file: relativePath,
			absolutePath: filePath,
			error: errorMessage,
			type: 'sass-migration-failed',
			step: 'sass-migration'
		})

		if (this.options.verbose) {
			console.log(chalk.gray(`    已标记为 AI 修复: ${relativePath}`))
		}
	}

	/**
	 * 分类错误类型
	 */
	categorizeError(errorMessage) {
		if (errorMessage.includes('Could not find Sass file') || errorMessage.includes('File to import not found')) {
			return 'import-path-error'
		}
		if (errorMessage.includes('Module loop') || errorMessage.includes('circular dependency')) {
			return 'circular-dependency'
		}
		if (errorMessage.includes('Undefined variable')) {
			return 'undefined-variable'
		}
		if (errorMessage.includes('@import rules are deprecated')) {
			return 'deprecated-import'
		}
		return 'unknown-error'
	}

	/**
	 * 获取需要 AI 修复的文件列表
	 */
	getFilesForAIRepair() {
		return this.filesForAIRepair || []
	}

	/**
	 * 尝试自动修复错误
	 */
	async tryAutoFix (filePath, error) {
		try {
			// 使用错误诊断模块分析问题
			const diagnostics = await this.errorDiagnostic.diagnose(error, filePath)

			let fixed = false
			for (const diagnostic of diagnostics) {
				if (diagnostic.autoFixable) {
					const fixResult = await this.errorDiagnostic.createFixSuggestion(diagnostic)
					if (fixResult) {
						await this.errorDiagnostic.applyFix(fixResult)
						fixed = true
						if (this.options.verbose) {
							console.log(chalk.gray(`    已修复: ${diagnostic.description}`))
						}
					}
				}
			}

			return { fixed }
		} catch (fixError) {
			if (this.options.verbose) {
				console.warn(chalk.yellow(`    自动修复失败: ${fixError.message}`))
			}
			return { fixed: false }
		}
	}

	/**
	 * 执行 sass-migrator 命令
	 */
	async executeSassMigrator (args) {
		return new Promise((resolve, reject) => {
			const child = spawn('sass-migrator', args, {
				cwd: this.projectPath,
				stdio: ['pipe', 'pipe', 'pipe']
			})

			let stdout = ''
			let stderr = ''

			child.stdout.on('data', (data) => {
				stdout += data.toString()
			})

			child.stderr.on('data', (data) => {
				stderr += data.toString()
			})

			child.on('close', (code) => {
				if (code === 0) {
					resolve(stdout)
				} else {
					reject(new Error(stderr || `sass-migrator exited with code ${code}`))
				}
			})

			child.on('error', (error) => {
				reject(new Error(`Failed to start sass-migrator: ${error.message}`))
			})
		})
	}

	/**
	 * 执行 Sass 迁移
	 */
	async migrate () {
		console.log(chalk.blue('🎨 开始 Sass 语法迁移...'))

		try {
			// 初始化增强功能
			await this.initialize()

			// 检查 sass-migrator 是否可用
			const sassMigratorAvailable = await this.isSassMigratorAvailable()
			if (!sassMigratorAvailable && !this.options.useEnhancedMigration) {
				throw new Error('sass-migrator 不可用，请安装: npm install -g sass-migrator')
			}

			if (!sassMigratorAvailable && this.options.useEnhancedMigration) {
				console.log(chalk.yellow('⚠️  sass-migrator 不可用，将使用内置迁移逻辑'))
			}

			// 查找所有 Sass 文件
			const sassFiles = await this.findSassFiles()
			this.stats.totalFiles = sassFiles.length

			if (sassFiles.length === 0) {
				console.log(chalk.gray('未找到需要迁移的 Sass/SCSS 文件'))
				return this.stats
			}

			console.log(chalk.blue(`找到 ${sassFiles.length} 个 Sass/SCSS 文件`))

			// 逐个处理文件
			for (const filePath of sassFiles) {
				await this.migrateFile(filePath)
			}

			// 保存失败记录
			if (this.failureLogger && this.stats.errorFiles > 0) {
				await this.failureLogger.saveFailures()
			}

			// 打印统计信息
			this.printStats()

			return this.stats

		} catch (error) {
			console.error(chalk.red(`Sass 迁移失败: ${error.message}`))
			throw error
		}
	}

	/**
	 * 打印统计信息
	 */
	printStats () {
		console.log('\n' + chalk.bold('📊 Sass 迁移统计:'))
		console.log(`总文件数: ${this.stats.totalFiles}`)
		console.log(`已处理: ${this.stats.processedFiles}`)
		console.log(`已跳过: ${this.stats.skippedFiles}`)
		console.log(`错误: ${this.stats.errorFiles}`)

		if (this.options.useEnhancedMigration) {
			console.log(`增强迁移: ${this.stats.enhancedMigrations}`)
			console.log(`自动修复: ${this.stats.autoFixes}`)
		}

		if (this.stats.errors.length > 0) {
			console.log(chalk.yellow('\n⚠️  错误文件:'))
			this.stats.errors.forEach(({ file, error }) => {
				console.log(chalk.red(`  ${path.relative(this.projectPath, file)}: ${error}`))
			})

			if (this.options.useEnhancedMigration) {
				console.log(chalk.gray('\n💡 提示: 启用了增强迁移功能，可以处理复杂的路径问题'))
			}
		}

		if (this.stats.processedFiles > 0) {
			console.log(chalk.green(`\n✅ ${this.stats.processedFiles} 个文件成功迁移到 @use 语法`))

			if (this.stats.enhancedMigrations > 0) {
				console.log(chalk.blue(`🚀 其中 ${this.stats.enhancedMigrations} 个文件使用了增强迁移功能`))
			}
		}
	}

	/**
	 * 恢复备份文件
	 */
	async restoreBackups () {
		try {
			const backupFiles = glob.sync('**/*.sass-backup', {
				cwd: this.projectPath,
				absolute: true
			})

			for (const backupFile of backupFiles) {
				const originalFile = backupFile.replace('.sass-backup', '')
				await fs.move(backupFile, originalFile, { overwrite: true })
				console.log(chalk.gray(`已恢复: ${path.relative(this.projectPath, originalFile)}`))
			}

			console.log(chalk.green(`✅ 已恢复 ${backupFiles.length} 个备份文件`))
		} catch (error) {
			console.error(chalk.red(`恢复备份失败: ${error.message}`))
			throw error
		}
	}

	/**
	 * 清理备份文件
	 */
	async cleanBackups () {
		try {
			const backupFiles = glob.sync('**/*.sass-backup', {
				cwd: this.projectPath,
				absolute: true
			})

			for (const backupFile of backupFiles) {
				await fs.remove(backupFile)
			}

			if (backupFiles.length > 0) {
				console.log(chalk.gray(`🧹 已清理 ${backupFiles.length} 个备份文件`))
			}
		} catch (error) {
			console.warn(chalk.yellow(`清理备份文件时出错: ${error.message}`))
		}
	}

	/**
	 * 获取统计信息
	 */
	getStats () {
		return { ...this.stats }
	}
}

module.exports = SassMigrator
