/**
 * 兼容性规则引擎
 * 用于定义和处理各种依赖兼容性问题
 */

class CompatRule {
	constructor(name, config) {
		this.name = name
		this.type = config.type // 'critical', 'incompatible', 'warning'
		this.package = config.package
		this.condition = config.condition
		this.requiredVersion = config.requiredVersion
		this.error = config.error
		this.solution = config.solution
		this.autoFix = config.autoFix || false
		this.configFix = config.configFix || null
	}

	/**
	 * 检查规则是否匹配
	 */
	matches(dependencies) {
		if (typeof this.condition === 'function') {
			return this.condition(dependencies)
		}
		return this.condition(dependencies)
	}

	/**
	 * 生成问题报告
	 */
	generateIssue(dependencies) {
		const currentVersion = dependencies[this.package]
		return {
			type: this.type,
			package: this.package,
			currentVersion,
			requiredVersion: this.requiredVersion,
			error: this.error,
			solution: this.solution,
			rule: this.name
		}
	}

	/**
	 * 执行自动修复
	 */
	async autoFix(packageJson) {
		if (!this.autoFix) return false

		if (this.requiredVersion) {
			// 升级依赖版本
			if (packageJson.devDependencies && packageJson.devDependencies[this.package]) {
				packageJson.devDependencies[this.package] = this.requiredVersion
				return true
			} else if (packageJson.dependencies && packageJson.dependencies[this.package]) {
				packageJson.dependencies[this.package] = this.requiredVersion
				return true
			}
		}

		return false
	}
}

/**
 * 兼容性规则管理器
 */
class CompatibilityRuleManager {
	constructor() {
		this.rules = new Map()
		this.initializeRules()
	}

	/**
	 * 初始化所有兼容性规则
	 */
	initializeRules() {
		// html-webpack-plugin 版本问题
		this.addRule('html-webpack-plugin-version', {
			type: 'critical',
			package: 'html-webpack-plugin',
			condition: (deps) => {
				const version = deps['html-webpack-plugin']
				if (!version) return false
				const cleanVersion = version.replace(/^[\^~>=<]+/, '')
				const majorVersion = parseInt(cleanVersion.split('.')[0])
				return majorVersion < 5
			},
			requiredVersion: '^5.6.0',
			error: 'htmlWebpackPlugin.getHooks is not a function',
			solution: 'Upgrade to version 5.6.0 or higher for webpack 5 compatibility',
			autoFix: true
		})

		// script-ext-html-webpack-plugin 不兼容问题
		this.addRule('script-ext-html-webpack-plugin-incompatible', {
			type: 'incompatible',
			package: 'script-ext-html-webpack-plugin',
			condition: (deps) => deps['script-ext-html-webpack-plugin'] !== undefined,
			error: 'Incompatible with html-webpack-plugin 5.x',
			solution: 'Remove this plugin and update vue.config.js configuration',
			configFix: {
				file: 'vue.config.js',
				action: 'remove',
				description: '移除 script-ext-html-webpack-plugin 配置',
				patterns: [
					{
						type: 'import',
						pattern: /(const\s+ScriptExtHtmlWebpackPlugin\s*=\s*require\s*\(\s*['"]script-ext-html-webpack-plugin['"]\s*\)\s*;?\s*)/g
					},
					{
						type: 'chainWebpack',
						pattern: /(\s*config\s*\.\s*plugin\s*\(\s*['"]ScriptExtHtmlWebpackPlugin['"]\s*\)\s*\.\s*after\s*\(\s*['"]html['"]\s*\)\s*\.\s*use\s*\(\s*['"]script-ext-html-webpack-plugin['"]\s*,\s*\[[^\]]*\]\s*\)\s*;?\s*)/g
					},
					{
						type: 'configureWebpack',
						pattern: /(\s*new\s+ScriptExtHtmlWebpackPlugin\s*\(\s*\{[^}]*\}\s*\)\s*,?\s*)/g
					}
				]
			}
		})

		// svg-sprite-loader 版本问题
		this.addRule('svg-sprite-loader-version', {
			type: 'critical',
			package: 'svg-sprite-loader',
			condition: (deps) => {
				const version = deps['svg-sprite-loader']
				if (!version) return false
				const cleanVersion = version.replace(/^[\^~>=<]+/, '')
				const majorVersion = parseInt(cleanVersion.split('.')[0])
				return majorVersion < 6
			},
			requiredVersion: '^6.0.11',
			error: 'Cannot find module \'webpack/lib/RuleSet\'',
			solution: 'Upgrade to version 6.0.11 or higher for webpack 5 compatibility',
			autoFix: true
		})

		// 添加更多规则...
		this.addRule('webpack-version', {
			type: 'critical',
			package: 'webpack',
			condition: (deps) => {
				const version = deps['webpack']
				if (!version) return false
				const cleanVersion = version.replace(/^[\^~>=<]+/, '')
				const majorVersion = parseInt(cleanVersion.split('.')[0])
				return majorVersion < 5
			},
			requiredVersion: '^5.89.0',
			error: 'Webpack 4 is not compatible with modern Vue 3 tooling',
			solution: 'Upgrade to webpack 5 for better Vue 3 support',
			autoFix: true
		})

		this.addRule('sass-loader-version', {
			type: 'critical',
			package: 'sass-loader',
			condition: (deps) => {
				const version = deps['sass-loader']
				if (!version) return false
				const cleanVersion = version.replace(/^[\^~>=<]+/, '')
				const majorVersion = parseInt(cleanVersion.split('.')[0])
				return majorVersion < 12
			},
			requiredVersion: '^13.3.0',
			error: 'sass-loader version too old for webpack 5',
			solution: 'Upgrade to sass-loader 13.x for webpack 5 compatibility',
			autoFix: true
		})

		this.addRule('eslint-version', {
			type: 'critical',
			package: 'eslint',
			condition: (deps) => {
				const version = deps['eslint']
				if (!version) return false
				const cleanVersion = version.replace(/^[\^~>=<]+/, '')
				const majorVersion = parseInt(cleanVersion.split('.')[0])
				return majorVersion < 8
			},
			requiredVersion: '^8.56.0',
			error: 'ESLint version too old for modern Vue 3 development',
			solution: 'Upgrade to ESLint 8.x for better Vue 3 support',
			autoFix: true
		})
	}

	/**
	 * 添加规则
	 */
	addRule(name, config) {
		this.rules.set(name, new CompatRule(name, config))
	}

	/**
	 * 获取规则
	 */
	getRule(name) {
		return this.rules.get(name)
	}

	/**
	 * 获取所有规则
	 */
	getAllRules() {
		return Array.from(this.rules.values())
	}

	/**
	 * 检查依赖兼容性
	 */
	checkCompatibility(dependencies) {
		const issues = []

		for (const rule of this.rules.values()) {
			if (rule.matches(dependencies)) {
				issues.push(rule.generateIssue(dependencies))
			}
		}

		return issues
	}

	async autoFixIssues(packageJson, issues) {
		let modified = false

		for (const issue of issues) {
			const rule = this.rules.get(issue.rule)
			if (rule && typeof rule.autoFix === 'function') {
				const fixed = await rule.autoFix(packageJson)
				if (fixed) {
					modified = true
				}
			}
		}

		return modified
	}

	/**
	 * 生成配置修复建议
	 */
	generateConfigSuggestions(issues) {
		const suggestions = []

		for (const issue of issues) {
			const rule = this.rules.get(issue.rule)
			if (rule && rule.configFix) {
				suggestions.push({
					...rule.configFix,
					package: issue.package,
					error: issue.error,
					solution: issue.solution
				})
			}
		}

		return suggestions
	}
}

module.exports = {
	CompatibilityRule: CompatRule,
	CompatibilityRuleManager
}
