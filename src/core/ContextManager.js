const chalk = require('chalk');
const MigrationContext = require('./MigrationContext');

/**
 * ContextManager - 上下文管理器
 * 
 * 负责协调和管理整个迁移过程中的上下文信息
 * 提供统一的接口来访问和更新各个组件的状态
 */
class ContextManager {
  constructor() {
    this.contexts = new Map(); // 支持多个并发迁移上下文
    this.activeContext = null;
    this.globalConfig = {};
    this.subscribers = new Map(); // 事件订阅者
  }

  /**
   * 创建新的迁移上下文
   */
  createContext(projectPath, options = {}) {
    const contextId = this.generateContextId(projectPath);
    const context = new MigrationContext(projectPath, options);
    
    // 设置上下文事件监听
    this.setupContextListeners(context, contextId);
    
    this.contexts.set(contextId, context);
    this.activeContext = contextId;
    
    console.log(chalk.blue(`🎯 创建迁移上下文: ${contextId}`));
    return { contextId, context };
  }

  /**
   * 获取上下文
   */
  getContext(contextId = null) {
    const id = contextId || this.activeContext;
    if (!id) {
      throw new Error('没有活动的迁移上下文');
    }
    
    const context = this.contexts.get(id);
    if (!context) {
      throw new Error(`上下文不存在: ${id}`);
    }
    
    return context;
  }

  /**
   * 设置活动上下文
   */
  setActiveContext(contextId) {
    if (!this.contexts.has(contextId)) {
      throw new Error(`上下文不存在: ${contextId}`);
    }
    
    this.activeContext = contextId;
    return this;
  }

  /**
   * 删除上下文
   */
  removeContext(contextId) {
    if (this.contexts.has(contextId)) {
      const context = this.contexts.get(contextId);
      context.removeAllListeners();
      this.contexts.delete(contextId);
      
      if (this.activeContext === contextId) {
        this.activeContext = null;
      }
      
      console.log(chalk.gray(`🗑️  删除迁移上下文: ${contextId}`));
    }
    
    return this;
  }

  /**
   * 设置全局配置
   */
  setGlobalConfig(config) {
    Object.assign(this.globalConfig, config);
    
    // 将全局配置应用到所有活动上下文
    for (const context of this.contexts.values()) {
      context.setConfig('global', this.globalConfig);
    }
    
    return this;
  }

  /**
   * 获取全局配置
   */
  getGlobalConfig() {
    return this.globalConfig;
  }

  /**
   * 订阅上下文事件
   */
  subscribe(eventType, callback, contextId = null) {
    const id = contextId || 'global';
    
    if (!this.subscribers.has(id)) {
      this.subscribers.set(id, new Map());
    }
    
    const contextSubscribers = this.subscribers.get(id);
    if (!contextSubscribers.has(eventType)) {
      contextSubscribers.set(eventType, []);
    }
    
    contextSubscribers.get(eventType).push(callback);
    
    return () => {
      // 返回取消订阅函数
      const callbacks = contextSubscribers.get(eventType);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    };
  }

  /**
   * 发布事件到订阅者
   */
  publish(eventType, data, contextId = null) {
    // 发布到特定上下文的订阅者
    if (contextId && this.subscribers.has(contextId)) {
      this.publishToSubscribers(this.subscribers.get(contextId), eventType, data);
    }
    
    // 发布到全局订阅者
    if (this.subscribers.has('global')) {
      this.publishToSubscribers(this.subscribers.get('global'), eventType, data);
    }
  }

  /**
   * 发布事件到订阅者列表
   */
  publishToSubscribers(subscriberMap, eventType, data) {
    if (subscriberMap.has(eventType)) {
      const callbacks = subscriberMap.get(eventType);
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(chalk.red(`事件回调执行失败: ${error.message}`));
        }
      });
    }
  }

  /**
   * 设置上下文事件监听
   */
  setupContextListeners(context, contextId) {
    // 监听所有上下文事件并转发给订阅者
    const eventTypes = [
      'context:updated',
      'phase:start',
      'phase:complete',
      'phase:failed',
      'phase:skipped',
      'file:processed',
      'ai:call',
      'ai:status:updated',
      'build:status:updated',
      'dependencies:updated',
      'project:detected',
      'config:updated',
      'tool:registered',
      'error:added',
      'warning:added',
      'migration:complete'
    ];

    eventTypes.forEach(eventType => {
      context.on(eventType, (...args) => {
        let eventData;

        // 处理不同的事件数据格式
        if (eventType === 'phase:start') {
          eventData = { contextId, phase: args[0] };
        } else if (eventType === 'phase:complete') {
          eventData = { contextId, phase: args[0], result: args[1] };
        } else if (eventType === 'phase:failed') {
          eventData = { contextId, phase: args[0], error: args[1] };
        } else if (typeof args[0] === 'object' && args[0] !== null) {
          eventData = { contextId, ...args[0] };
        } else {
          eventData = { contextId, data: args[0] };
        }

        this.publish(eventType, eventData, contextId);
      });
    });
  }

  /**
   * 生成上下文ID
   */
  generateContextId(projectPath) {
    const timestamp = Date.now();
    const pathHash = require('crypto')
      .createHash('md5')
      .update(projectPath)
      .digest('hex')
      .substring(0, 8);
    
    return `migration_${pathHash}_${timestamp}`;
  }

  /**
   * 获取所有上下文的状态摘要
   */
  getAllContextsSummary() {
    const summary = {
      totalContexts: this.contexts.size,
      activeContext: this.activeContext,
      contexts: {}
    };

    for (const [contextId, context] of this.contexts.entries()) {
      summary.contexts[contextId] = context.getStatusSummary();
    }

    return summary;
  }

  /**
   * 创建上下文快照
   */
  async createSnapshot(contextId = null) {
    const context = this.getContext(contextId);
    const snapshotPath = await context.saveToFile();
    
    console.log(chalk.green(`📸 上下文快照已创建: ${snapshotPath}`));
    return snapshotPath;
  }

  /**
   * 从快照恢复上下文
   */
  async restoreFromSnapshot(snapshotPath) {
    const context = await MigrationContext.loadFromFile(snapshotPath);
    const contextId = this.generateContextId(context.project.path);
    
    this.setupContextListeners(context, contextId);
    this.contexts.set(contextId, context);
    this.activeContext = contextId;
    
    console.log(chalk.green(`🔄 上下文已从快照恢复: ${contextId}`));
    return { contextId, context };
  }

  /**
   * 清理所有上下文
   */
  cleanup() {
    for (const contextId of this.contexts.keys()) {
      this.removeContext(contextId);
    }
    
    this.subscribers.clear();
    this.activeContext = null;
    
    console.log(chalk.gray('🧹 上下文管理器已清理'));
  }

  /**
   * 获取上下文统计信息
   */
  getStatistics() {
    const stats = {
      totalContexts: this.contexts.size,
      activeContexts: Array.from(this.contexts.keys()),
      globalSubscribers: this.subscribers.get('global')?.size || 0,
      totalSubscribers: 0
    };

    // 计算总订阅者数量
    for (const subscriberMap of this.subscribers.values()) {
      for (const callbacks of subscriberMap.values()) {
        stats.totalSubscribers += callbacks.length;
      }
    }

    return stats;
  }

  /**
   * 监控上下文健康状态
   */
  healthCheck() {
    const health = {
      status: 'healthy',
      issues: [],
      contexts: {}
    };

    for (const [contextId, context] of this.contexts.entries()) {
      const contextHealth = {
        status: 'healthy',
        issues: []
      };

      // 检查是否有关键错误
      if (context.issues.criticalErrors.length > 0) {
        contextHealth.status = 'critical';
        contextHealth.issues.push(`${context.issues.criticalErrors.length} 个关键错误`);
      }

      // 检查是否有过多失败
      if (context.phases.failed.length > context.phases.completed.length) {
        contextHealth.status = 'unhealthy';
        contextHealth.issues.push('失败阶段过多');
      }

      // 检查 AI 成功率
      if (context.ai.calls > 10 && context.ai.successRate < 50) {
        contextHealth.status = 'warning';
        contextHealth.issues.push('AI 成功率过低');
      }

      health.contexts[contextId] = contextHealth;

      if (contextHealth.status !== 'healthy') {
        health.status = 'warning';
        health.issues.push(`上下文 ${contextId}: ${contextHealth.issues.join(', ')}`);
      }
    }

    return health;
  }

  /**
   * 导出所有上下文数据
   */
  async exportAll(outputDir) {
    const fs = require('fs-extra');
    const path = require('path');

    await fs.ensureDir(outputDir);
    
    const exportData = {
      timestamp: new Date(),
      globalConfig: this.globalConfig,
      contexts: {}
    };

    for (const [contextId, context] of this.contexts.entries()) {
      exportData.contexts[contextId] = context.export();
    }

    const exportPath = path.join(outputDir, 'migration-contexts-export.json');
    await fs.writeJson(exportPath, exportData, { spaces: 2 });

    console.log(chalk.blue(`📦 所有上下文已导出: ${exportPath}`));
    return exportPath;
  }
}

// 创建全局单例实例
const contextManager = new ContextManager();

module.exports = {
  ContextManager,
  contextManager // 导出单例实例
};
