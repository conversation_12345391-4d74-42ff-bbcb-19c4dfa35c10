const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const EventEmitter = require('events');

/**
 * MigrationContext - 迁移上下文管理器
 * 
 * 负责管理整个迁移过程中的状态、配置、结果和上下文信息
 * 提供统一的接口来访问和更新迁移过程中的各种数据
 */
class MigrationContext extends EventEmitter {
  constructor(projectPath, options = {}) {
    super();
    
    this.projectPath = path.resolve(projectPath);
    this.options = options;
    this.createdAt = new Date();
    
    // 初始化上下文数据结构
    this.initializeContext();
    
    // 设置事件监听器
    this.setupEventListeners();
  }

  /**
   * 初始化上下文数据结构
   */
  initializeContext() {
    // 项目信息
    this.project = {
      path: this.projectPath,
      type: null,
      name: null,
      version: null,
      packageJson: null,
      detectedFramework: null,
      confidence: 0
    };

    // 迁移配置
    this.config = {
      mode: this.options.mode || 'auto', // auto, manual, guided
      sourceToTargetMode: this.options.sourceToTargetMode || false,
      sourcePath: this.options.sourcePath || null,
      targetPath: this.options.targetPath || null,
      workingPath: null,
      presetConfig: null,
      finalOptions: null,
      aiProvider: null,
      aiApiKey: null
    };

    // 迁移阶段状态
    this.phases = {
      current: null,
      completed: [],
      failed: [],
      skipped: [],
      results: {}
    };

    // 文件状态跟踪
    this.files = {
      total: 0,
      processed: 0,
      modified: 0,
      failed: [],
      backup: {},
      dependencies: {}
    };

    // 依赖管理
    this.dependencies = {
      original: {},
      upgraded: {},
      mapped: {},
      incompatible: [],
      conflicts: []
    };

    // AI 服务状态
    this.ai = {
      enabled: false,
      provider: null,
      model: null,
      calls: 0,
      successRate: 0,
      errors: [],
      stats: {
        attempted: 0,
        success: 0,
        failed: 0,
        skipped: 0
      }
    };

    // 构建状态
    this.build = {
      status: 'unknown', // unknown, success, failed, in-progress
      command: null,
      attempts: 0,
      errors: [],
      lastError: null,
      fixAttempts: 0
    };

    // 错误和警告
    this.issues = {
      errors: [],
      warnings: [],
      criticalErrors: [],
      resolved: []
    };

    // 统计信息
    this.stats = {
      startTime: Date.now(),
      endTime: null,
      duration: null,
      totalSteps: 0,
      completedSteps: 0,
      successRate: 0,
      performance: {}
    };

    // 工具状态
    this.tools = {
      projectDetector: null,
      packageUpgrader: null,
      codeMigrator: null,
      buildFixer: null,
      aiRepairer: null,
      eslintFixer: null,
      sassMigrator: null
    };
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    this.on('phase:start', (phaseName) => {
      this.phases.current = phaseName;
      this.emit('context:updated', { type: 'phase:start', phase: phaseName });
    });

    this.on('phase:complete', (phaseName, result) => {
      this.phases.completed.push(phaseName);
      this.phases.results[phaseName] = result;
      this.phases.current = null;
      this.stats.completedSteps++;
      this.emit('context:updated', { type: 'phase:complete', phase: phaseName, result });
    });

    this.on('phase:failed', (phaseName, error) => {
      this.phases.failed.push(phaseName);
      this.addError(error, phaseName);
      this.phases.current = null;
      this.emit('context:updated', { type: 'phase:failed', phase: phaseName, error });
    });

    this.on('file:processed', (filePath, result) => {
      this.files.processed++;
      if (result.modified) {
        this.files.modified++;
      }
      if (result.failed) {
        this.files.failed.push({ file: filePath, error: result.error });
      }
      this.emit('context:updated', { type: 'file:processed', file: filePath, result });
    });

    this.on('ai:call', (callInfo) => {
      this.ai.calls++;
      this.ai.stats.attempted++;
      if (callInfo.success) {
        this.ai.stats.success++;
      } else {
        this.ai.stats.failed++;
        this.ai.errors.push(callInfo.error);
      }
      this.updateAISuccessRate();
      this.emit('context:updated', { type: 'ai:call', callInfo });
    });
  }

  /**
   * 设置项目信息
   */
  setProjectInfo(projectInfo) {
    Object.assign(this.project, projectInfo);
    this.emit('project:detected', projectInfo);
    return this;
  }

  /**
   * 设置配置信息
   */
  setConfig(configKey, value) {
    if (typeof configKey === 'object') {
      Object.assign(this.config, configKey);
    } else {
      this.config[configKey] = value;
    }
    this.emit('config:updated', { key: configKey, value });
    return this;
  }

  /**
   * 获取配置值
   */
  getConfig(key) {
    return key ? this.config[key] : this.config;
  }

  /**
   * 设置工作路径
   */
  setWorkingPath(workingPath) {
    this.config.workingPath = path.resolve(workingPath);
    return this;
  }

  /**
   * 获取工作路径
   */
  getWorkingPath() {
    return this.config.workingPath || this.projectPath;
  }

  /**
   * 注册工具实例
   */
  registerTool(toolName, toolInstance) {
    this.tools[toolName] = toolInstance;
    this.emit('tool:registered', { name: toolName, instance: toolInstance });
    return this;
  }

  /**
   * 获取工具实例
   */
  getTool(toolName) {
    return this.tools[toolName];
  }

  /**
   * 开始阶段
   */
  startPhase(phaseName, totalSteps = null) {
    if (totalSteps) {
      this.stats.totalSteps = totalSteps;
    }
    this.emit('phase:start', phaseName);
    return this;
  }

  /**
   * 完成阶段
   */
  completePhase(phaseName, result = {}) {
    this.emit('phase:complete', phaseName, result);
    return this;
  }

  /**
   * 阶段失败
   */
  failPhase(phaseName, error) {
    this.emit('phase:failed', phaseName, error);
    return this;
  }

  /**
   * 跳过阶段
   */
  skipPhase(phaseName, reason) {
    this.phases.skipped.push({ phase: phaseName, reason });
    this.emit('phase:skipped', phaseName, reason);
    return this;
  }

  /**
   * 添加错误
   */
  addError(error, context = null) {
    const errorInfo = {
      message: error.message || error,
      context,
      timestamp: new Date(),
      stack: error.stack
    };
    
    this.issues.errors.push(errorInfo);
    
    if (context && context.includes('critical')) {
      this.issues.criticalErrors.push(errorInfo);
    }
    
    this.emit('error:added', errorInfo);
    return this;
  }

  /**
   * 添加警告
   */
  addWarning(warning, context = null) {
    const warningInfo = {
      message: warning.message || warning,
      context,
      timestamp: new Date()
    };
    
    this.issues.warnings.push(warningInfo);
    this.emit('warning:added', warningInfo);
    return this;
  }

  /**
   * 更新文件状态
   */
  updateFileStatus(filePath, status) {
    this.emit('file:processed', filePath, status);
    return this;
  }

  /**
   * 更新依赖信息
   */
  updateDependencies(dependencyInfo) {
    Object.assign(this.dependencies, dependencyInfo);
    this.emit('dependencies:updated', dependencyInfo);
    return this;
  }

  /**
   * 设置 AI 服务状态
   */
  setAIStatus(aiInfo) {
    Object.assign(this.ai, aiInfo);
    this.emit('ai:status:updated', aiInfo);
    return this;
  }

  /**
   * 记录 AI 调用
   */
  recordAICall(callInfo) {
    this.emit('ai:call', callInfo);
    return this;
  }

  /**
   * 更新 AI 成功率
   */
  updateAISuccessRate() {
    if (this.ai.stats.attempted > 0) {
      this.ai.successRate = (this.ai.stats.success / this.ai.stats.attempted) * 100;
    }
  }

  /**
   * 更新构建状态
   */
  updateBuildStatus(buildInfo) {
    Object.assign(this.build, buildInfo);
    this.emit('build:status:updated', buildInfo);
    return this;
  }

  /**
   * 获取当前状态摘要
   */
  getStatusSummary() {
    return {
      project: this.project,
      currentPhase: this.phases.current,
      completedPhases: this.phases.completed.length,
      totalPhases: this.stats.totalSteps,
      filesProcessed: this.files.processed,
      filesModified: this.files.modified,
      errorCount: this.issues.errors.length,
      warningCount: this.issues.warnings.length,
      aiCalls: this.ai.calls,
      aiSuccessRate: this.ai.successRate,
      buildStatus: this.build.status,
      duration: this.getDuration()
    };
  }

  /**
   * 获取持续时间
   */
  getDuration() {
    const endTime = this.stats.endTime || Date.now();
    return endTime - this.stats.startTime;
  }

  /**
   * 完成迁移
   */
  completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.getDuration();
    this.stats.successRate = this.calculateSuccessRate();
    
    this.emit('migration:complete', this.getStatusSummary());
    return this;
  }

  /**
   * 计算成功率
   */
  calculateSuccessRate() {
    if (this.stats.totalSteps === 0) return 0;
    return (this.phases.completed.length / this.stats.totalSteps) * 100;
  }

  /**
   * 导出上下文数据
   */
  export() {
    return {
      project: this.project,
      config: this.config,
      phases: this.phases,
      files: this.files,
      dependencies: this.dependencies,
      ai: this.ai,
      build: this.build,
      issues: this.issues,
      stats: this.stats,
      createdAt: this.createdAt,
      exportedAt: new Date()
    };
  }

  /**
   * 保存上下文到文件
   */
  async saveToFile(filePath) {
    const contextData = this.export();
    const outputPath = filePath || path.join(this.getWorkingPath(), 'migration-context.json');
    
    await fs.ensureDir(path.dirname(outputPath));
    await fs.writeJson(outputPath, contextData, { spaces: 2 });
    
    console.log(chalk.blue(`📄 迁移上下文已保存: ${outputPath}`));
    return outputPath;
  }

  /**
   * 从文件加载上下文
   */
  static async loadFromFile(filePath) {
    const contextData = await fs.readJson(filePath);
    const context = new MigrationContext(contextData.project.path);
    
    // 恢复状态
    Object.assign(context.project, contextData.project);
    Object.assign(context.config, contextData.config);
    Object.assign(context.phases, contextData.phases);
    Object.assign(context.files, contextData.files);
    Object.assign(context.dependencies, contextData.dependencies);
    Object.assign(context.ai, contextData.ai);
    Object.assign(context.build, contextData.build);
    Object.assign(context.issues, contextData.issues);
    Object.assign(context.stats, contextData.stats);
    
    console.log(chalk.green(`✅ 迁移上下文已加载: ${filePath}`));
    return context;
  }
}

module.exports = MigrationContext;
