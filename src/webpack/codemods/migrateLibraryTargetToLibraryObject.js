/**
 * Webpack v5 Codemod: migrate-library-target-to-library-object
 * 
 * 将 webpack 4 的 output.library 和 output.libraryTarget 配置
 * 转换为 webpack 5 的 output.library 对象格式
 * 
 * 转换示例：
 * // webpack 4
 * {
 *   output: {
 *     library: 'MyLibrary',
 *     libraryTarget: 'commonjs2'
 *   }
 * }
 * 
 * // webpack 5
 * {
 *   output: {
 *     library: {
 *       name: 'MyLibrary',
 *       type: 'commonjs2'
 *     }
 *   }
 * }
 */

function transform(file, api, options) {
  const j = api.jscodeshift;
  const root = j(file.source);
  let dirtyFlag = false;

  // 查找 module.exports 赋值表达式
  root
    .find(j.AssignmentExpression, {
      left: {
        object: { name: "module" },
        property: { name: "exports" }
      }
    })
    .forEach((path) => {
      // 获取 output 属性
      const outputProperty = path
        .get("right", "properties")
        .filter((prop) => prop.node.key.name === "output")[0];

      if (!outputProperty) return;

      const outputObject = outputProperty.get("value");
      
      // 查找 library 和 libraryTarget 属性
      const libraryProperty = outputObject
        .get("properties")
        .filter((prop) => prop.node.key.name === "library")[0];

      const libraryTargetProperty = outputObject
        .get("properties")
        .filter((prop) => prop.node.key.name === "libraryTarget")[0];

      if (!libraryProperty) return;

      const libraryName = libraryProperty.node.value.value;
      const libraryType = libraryTargetProperty 
        ? libraryTargetProperty.node.value.value 
        : undefined;

      // 创建新的 library 对象
      const libraryObjectProperties = [
        j.property.from({
          kind: "init",
          key: j.identifier("name"),
          value: j.literal(libraryName)
        })
      ];

      // 添加 type 属性（如果存在 libraryTarget）
      if (libraryType) {
        libraryObjectProperties.push(
          j.property.from({
            kind: "init",
            key: j.identifier("type"),
            value: j.literal(libraryType)
          })
        );
      }

      // 替换 library 属性为对象格式
      libraryProperty.replace(
        j.property.from({
          kind: "init",
          key: j.identifier("library"),
          value: j.objectExpression(libraryObjectProperties, true)
        })
      );

      // 移除 libraryTarget 属性
      if (libraryTargetProperty) {
        j(libraryTargetProperty).remove();
      }

      dirtyFlag = true;
    });

  // 处理其他可能的配置格式（如直接的对象字面量）
  root
    .find(j.ObjectExpression)
    .forEach((path) => {
      const outputProperty = path.node.properties.find(
        prop => prop.key && prop.key.name === 'output'
      );

      if (!outputProperty || !outputProperty.value || outputProperty.value.type !== 'ObjectExpression') {
        return;
      }

      const outputProps = outputProperty.value.properties;
      const libraryProp = outputProps.find(prop => prop.key && prop.key.name === 'library');
      const libraryTargetProp = outputProps.find(prop => prop.key && prop.key.name === 'libraryTarget');

      if (!libraryProp || libraryProp.value.type === 'ObjectExpression') {
        return; // 已经是对象格式或不存在
      }

      const libraryName = libraryProp.value.value;
      const libraryType = libraryTargetProp ? libraryTargetProp.value.value : undefined;

      // 创建新的 library 对象
      const libraryObjectProperties = [
        j.property('init', j.identifier('name'), j.literal(libraryName))
      ];

      if (libraryType) {
        libraryObjectProperties.push(
          j.property('init', j.identifier('type'), j.literal(libraryType))
        );
      }

      // 替换 library 属性
      libraryProp.value = j.objectExpression(libraryObjectProperties);

      // 移除 libraryTarget 属性
      if (libraryTargetProp) {
        const index = outputProps.indexOf(libraryTargetProp);
        if (index > -1) {
          outputProps.splice(index, 1);
        }
      }

      dirtyFlag = true;
    });

  return dirtyFlag ? root.toSource({ 
    quote: 'single', 
    trailingComma: true,
    reuseParsers: true
  }) : undefined;
}

module.exports = transform;
