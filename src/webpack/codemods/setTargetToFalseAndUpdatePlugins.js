/**
 * Webpack v5 Codemod: set-target-to-false-and-update-plugins
 * 
 * 将 webpack 配置中的 target 函数调用转换为 false，
 * 并将原函数调用移动到 plugins 数组中
 * 
 * 转换示例：
 * // webpack 4
 * {
 *   target: WebExtensionTarget(nodeConfig)
 * }
 * 
 * // webpack 5
 * {
 *   target: false,
 *   plugins: [
 *     WebExtensionTarget(nodeConfig)
 *   ]
 * }
 */

function transform(file, api, options) {
  const j = api.jscodeshift;
  const root = j(file.source);
  let dirtyFlag = false;

  // 查找 module.exports 赋值表达式
  root
    .find(j.AssignmentExpression, {
      left: {
        type: "MemberExpression",
        object: { name: "module" },
        property: { name: "exports" }
      }
    })
    .forEach((path) => {
      const right = path.node.right;

      // 确保右侧是对象表达式
      if (j.ObjectExpression.check(right)) {
        const properties = right.properties;
        let targetIndex = -1;
        let targetValue = null;

        // 查找 target 属性
        properties.forEach((prop, index) => {
          if (
            j.ObjectProperty.check(prop) &&
            j.Identifier.check(prop.key) &&
            prop.key.name === "target"
          ) {
            // 检查是否是函数调用
            if (j.CallExpression.check(prop.value)) {
              targetIndex = index;
              targetValue = prop.value;
            }
          }
        });

        if (targetIndex !== -1 && targetValue !== null) {
          // 将 target 属性值替换为 false
          properties[targetIndex].value = j.booleanLiteral(false);

          // 查找现有的 plugins 属性
          let pluginsIndex = -1;
          properties.forEach((prop, index) => {
            if (
              j.ObjectProperty.check(prop) &&
              j.Identifier.check(prop.key) &&
              prop.key.name === "plugins"
            ) {
              pluginsIndex = index;
            }
          });

          if (pluginsIndex !== -1) {
            // 如果已存在 plugins 属性，添加到数组中
            const pluginsProperty = properties[pluginsIndex];
            if (j.ArrayExpression.check(pluginsProperty.value)) {
              pluginsProperty.value.elements.push(targetValue);
            }
          } else {
            // 在 target 属性后插入新的 plugins 属性
            properties.splice(
              targetIndex + 1,
              0,
              j.objectProperty(
                j.identifier("plugins"),
                j.arrayExpression([targetValue])
              )
            );
          }

          dirtyFlag = true;
        }
      }
    });

  // 处理其他可能的配置格式（如直接的对象字面量）
  root
    .find(j.ObjectExpression)
    .forEach((path) => {
      const properties = path.node.properties;
      let targetIndex = -1;
      let targetValue = null;

      // 查找 target 属性
      properties.forEach((prop, index) => {
        if (
          prop.key &&
          prop.key.name === "target" &&
          prop.value &&
          prop.value.type === "CallExpression"
        ) {
          targetIndex = index;
          targetValue = prop.value;
        }
      });

      if (targetIndex !== -1 && targetValue !== null) {
        // 将 target 属性值替换为 false
        properties[targetIndex].value = j.booleanLiteral(false);

        // 查找现有的 plugins 属性
        let pluginsIndex = -1;
        properties.forEach((prop, index) => {
          if (prop.key && prop.key.name === "plugins") {
            pluginsIndex = index;
          }
        });

        if (pluginsIndex !== -1) {
          // 如果已存在 plugins 属性，添加到数组中
          const pluginsProperty = properties[pluginsIndex];
          if (pluginsProperty.value && pluginsProperty.value.type === "ArrayExpression") {
            pluginsProperty.value.elements.push(targetValue);
          }
        } else {
          // 在 target 属性后插入新的 plugins 属性
          properties.splice(
            targetIndex + 1,
            0,
            j.property('init', j.identifier("plugins"), j.arrayExpression([targetValue]))
          );
        }

        dirtyFlag = true;
      }
    });

  return dirtyFlag ? root.toSource({ 
    trailingComma: true,
    reuseParsers: true
  }) : undefined;
}

module.exports = transform;
