const path = require('path');
const fs = require('fs-extra');

/**
 * RuntimeErrorInjectionPlugin - Webpack插件
 * 
 * 功能：
 * 1. 在开发模式下自动注入运行时错误处理代码
 * 2. 在HTML中插入错误监控脚本
 * 3. 配置Vue错误处理器
 * 4. 支持热重载时的错误监控
 */
class RuntimeErrorInjectionPlugin {
  constructor(options = {}) {
    this.options = {
      enabled: true,
      endpoint: '/__runtime_errors__',
      injectInProduction: false,
      verbose: false,
      ...options
    };
  }

  apply(compiler) {
    const pluginName = 'RuntimeErrorInjectionPlugin';
    
    // 只在开发模式或明确启用时注入
    if (!this.shouldInject(compiler)) {
      return;
    }

    // 在编译开始时注入错误处理代码
    compiler.hooks.compilation.tap(pluginName, (compilation) => {
      // 处理HTML文件注入
      if (compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing) {
        compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing.tapAsync(
          pluginName,
          (data, callback) => {
            this.injectIntoHTML(data);
            callback();
          }
        );
      }

      // 处理入口文件注入
      compilation.hooks.beforeChunkAssets.tap(pluginName, () => {
        this.injectIntoEntryPoints(compilation);
      });
    });

    // 在开发服务器启动时输出信息
    compiler.hooks.done.tap(pluginName, (stats) => {
      if (this.options.verbose && !stats.hasErrors()) {
        console.log(`\n✅ Runtime Error Handler injected successfully`);
        console.log(`   Error endpoint: ${this.options.endpoint}`);
      }
    });
  }

  /**
   * 判断是否应该注入错误处理代码
   */
  shouldInject(compiler) {
    if (!this.options.enabled) {
      return false;
    }

    const isProduction = compiler.options.mode === 'production';
    return !isProduction || this.options.injectInProduction;
  }

  /**
   * 在HTML中注入错误处理脚本
   */
  injectIntoHTML(data) {
    const injectionScript = this.generateInjectionScript();
    
    // 在head标签结束前注入
    if (data.html.includes('</head>')) {
      data.html = data.html.replace(
        '</head>',
        `  <script>${injectionScript}</script>\n</head>`
      );
    } else {
      // 如果没有head标签，在body开始后注入
      data.html = data.html.replace(
        '<body>',
        `<body>\n  <script>${injectionScript}</script>`
      );
    }

    if (this.options.verbose) {
      console.log('Runtime error handler injected into HTML');
    }
  }

  /**
   * 在入口点注入错误处理代码
   */
  injectIntoEntryPoints(compilation) {
    const injectionCode = this.generateEntryInjectionCode();
    
    // 为每个入口点添加错误处理代码
    Object.keys(compilation.entries).forEach(entryName => {
      const entry = compilation.entries[entryName];
      if (entry && entry.dependencies) {
        // 创建虚拟模块来注入代码
        this.createVirtualModule(compilation, entryName, injectionCode);
      }
    });
  }

  /**
   * 创建虚拟模块注入代码
   */
  createVirtualModule(compilation, entryName, code) {
    const moduleId = `runtime-error-handler-${entryName}`;
    const source = `
// Runtime Error Handler for ${entryName}
${code}
`;

    // 添加到编译资源中
    compilation.assets[`${moduleId}.js`] = {
      source: () => source,
      size: () => source.length
    };
  }

  /**
   * 生成HTML注入脚本
   */
  generateInjectionScript() {
    const endpoint = this.options.endpoint;
    
    return `
// Vue Runtime Error Handler - Auto-injected by Webpack Plugin
(function() {
  const RUNTIME_ERROR_ENDPOINT = '${endpoint}';
  
  // 发送错误到服务器
  function sendErrorToServer(errorData) {
    if (typeof fetch !== 'undefined') {
      fetch(RUNTIME_ERROR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData)
      }).catch(err => {
        console.warn('Failed to send runtime error to server:', err);
      });
    }
  }
  
  // 获取组件追踪信息
  function getComponentTrace(vm) {
    const trace = [];
    let current = vm;
    
    while (current && trace.length < 10) { // 限制追踪深度
      if (current.$options) {
        trace.push({
          name: current.$options.name || current.$options._componentTag || 'Anonymous',
          file: current.$options.__file || 'unknown',
          line: 0
        });
      }
      current = current.$parent;
    }
    
    return trace;
  }
  
  // 等待Vue加载完成后设置错误处理器
  function setupVueErrorHandlers() {
    if (typeof window !== 'undefined' && window.Vue) {
      // Vue 2/3 兼容的错误处理器
      if (window.Vue.config) {
        // Vue 2 风格
        window.Vue.config.errorHandler = function(err, vm, info) {
          const errorData = {
            message: err.message || err.toString(),
            fileName: err.fileName || (err.stack && err.stack.split('\\n')[1]) || 'unknown',
            lineNumber: err.lineNumber || 0,
            columnNumber: err.columnNumber || 0,
            stack: err.stack || '',
            vueInfo: info || '',
            componentTrace: vm ? getComponentTrace(vm) : [],
            timestamp: new Date().toISOString(),
            type: 'vue-error'
          };
          
          console.error('Vue Runtime Error:', err);
          sendErrorToServer(errorData);
        };
        
        window.Vue.config.warnHandler = function(msg, vm, trace) {
          const errorData = {
            message: msg,
            fileName: 'vue-warning',
            lineNumber: 0,
            columnNumber: 0,
            stack: trace || '',
            vueInfo: 'warning',
            componentTrace: vm ? getComponentTrace(vm) : [],
            timestamp: new Date().toISOString(),
            type: 'vue-warning'
          };
          
          console.warn('Vue Warning:', msg);
          sendErrorToServer(errorData);
        };
      }
    }
  }
  
  // 全局错误处理器
  window.addEventListener('error', function(event) {
    const errorData = {
      message: event.message || 'Unknown error',
      fileName: event.filename || 'unknown',
      lineNumber: event.lineno || 0,
      columnNumber: event.colno || 0,
      stack: event.error ? event.error.stack : '',
      timestamp: new Date().toISOString(),
      type: 'javascript-error'
    };
    
    sendErrorToServer(errorData);
  });
  
  // Promise rejection处理器
  window.addEventListener('unhandledrejection', function(event) {
    const reason = event.reason;
    const errorData = {
      message: reason ? (reason.message || reason.toString()) : 'Unhandled Promise Rejection',
      fileName: 'promise',
      lineNumber: 0,
      columnNumber: 0,
      stack: reason && reason.stack ? reason.stack : '',
      timestamp: new Date().toISOString(),
      type: 'promise-rejection'
    };
    
    sendErrorToServer(errorData);
  });
  
  // 立即尝试设置Vue错误处理器
  setupVueErrorHandlers();
  
  // 如果Vue还没加载，等待DOM加载完成后再试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupVueErrorHandlers);
  } else {
    // 延迟一点再试，给Vue时间加载
    setTimeout(setupVueErrorHandlers, 100);
  }
})();
`;
  }

  /**
   * 生成入口点注入代码
   */
  generateEntryInjectionCode() {
    return `
// Runtime Error Handler Entry Point Injection
console.log('Runtime Error Handler initialized for entry point');
`;
  }
}

module.exports = RuntimeErrorInjectionPlugin;
