const chalk = require('chalk');
const ora = require('ora');

/**
 * 迁移阶段基类
 * 所有迁移阶段都应该继承此类
 */
class MigrationPhase {
	constructor(name, projectPath, options = {}) {
		this.name = name;
		this.projectPath = projectPath;
		this.options = options;
		this.spinner = null;
		this.startTime = null;
		this.endTime = null;
		this.result = null;
		this.errors = [];
	}

	/**
	 * 执行阶段 - 子类必须实现
	 * @param {Object} context - 上下文数据，包含前面阶段的结果
	 * @returns {Object} 阶段执行结果
	 */
	async execute(context = {}) {
		throw new Error(`Phase ${this.name} must implement execute() method`);
	}

	/**
	 * 开始阶段执行
	 */
	async start(context = {}) {
		this.startTime = Date.now();
		this.spinner = ora(`${this.name}...`).start();

		try {
			console.log(chalk.blue(`\n🔄 开始${this.name}...`));

			// 执行阶段前的准备工作
			await this.beforeExecute(context);

			// 执行主要逻辑
			this.result = await this.execute(context);

			// 执行阶段后的清理工作
			await this.afterExecute(context);

			this.endTime = Date.now();
			this.spinner.succeed(`${this.name}完成 (${this.getDuration()}ms)`);

			if (this.options.verbose && this.result) {
				this.logResult();
			}

			return this.result;

		} catch (error) {
			this.endTime = Date.now();
			this.errors.push(error);
			this.spinner.fail(`${this.name}失败: ${error.message}`);

			// 根据错误严重程度决定是否继续
			if (this.isCriticalError(error)) {
				throw error;
			} else {
				console.log(chalk.yellow(`⚠️  ${this.name}失败，但继续后续阶段`));
				return this.getFailureResult(error);
			}
		}
	}

	/**
	 * 阶段执行前的准备工作
	 */
	async beforeExecute(context) {
		// 子类可以重写此方法
	}

	/**
	 * 阶段执行后的清理工作
	 */
	async afterExecute(context) {
		// 子类可以重写此方法
	}

	/**
	 * 判断是否为关键错误
	 * @param {Error} error
	 * @returns {boolean}
	 */
	isCriticalError(error) {
		// 默认所有错误都不是关键错误，子类可以重写
		return false;
	}

	/**
	 * 获取失败时的结果
	 * @param {Error} error
	 * @returns {Object}
	 */
	getFailureResult(error) {
		return {
			success: false,
			error: error.message,
			phase: this.name
		};
	}

	/**
	 * 记录结果
	 */
	logResult() {
		if (this.result && typeof this.result === 'object') {
			console.log(chalk.gray('  结果详情:'));
			Object.entries(this.result).forEach(([key, value]) => {
				if (typeof value === 'number' || typeof value === 'string') {
					console.log(chalk.gray(`    ${key}: ${value}`));
				}
			});
		}
	}

	/**
	 * 获取执行时长
	 */
	getDuration() {
		return this.endTime - this.startTime;
	}

	/**
	 * 获取阶段统计信息
	 */
	getStats() {
		return {
			name: this.name,
			duration: this.getDuration(),
			success: this.errors.length === 0,
			errors: this.errors.length,
			result: this.result
		};
	}

	/**
	 * 检查是否可以跳过此阶段
	 * @param {Object} context
	 * @returns {boolean}
	 */
	canSkip(context) {
		// 子类可以重写此方法来实现跳过逻辑
		return false;
	}

	/**
	 * 跳过阶段
	 */
	skip(reason = '') {
		console.log(chalk.gray(`⏭️  跳过${this.name}${reason ? ': ' + reason : ''}`));
		return {
			success: true,
			skipped: true,
			reason,
			phase: this.name
		};
	}

	/**
	 * 获取阶段依赖
	 * @returns {Array} 依赖的阶段名称列表
	 */
	getDependencies() {
		return [];
	}

	/**
	 * 获取关键依赖（必须成功的依赖）
	 * @returns {Array} 关键依赖的阶段名称列表
	 */
	getCriticalDependencies() {
		// 默认所有依赖都是关键的，子类可以重写
		return this.getDependencies();
	}

	/**
	 * 验证上下文是否满足执行条件
	 * @param {Object} context
	 * @returns {boolean}
	 */
	validateContext(context) {
		const dependencies = this.getDependencies();
		const criticalDependencies = this.getCriticalDependencies();

		// 检查关键依赖
		for (const dep of criticalDependencies) {
			if (!context[dep] || context[dep].success === false) {
				throw new Error(`阶段 ${this.name} 依赖 ${dep} 阶段的成功执行`);
			}
		}

		// 检查非关键依赖，只警告
		for (const dep of dependencies) {
			if (!criticalDependencies.includes(dep)) {
				if (!context[dep] || context[dep].success === false) {
					console.log(chalk.yellow(`⚠️  阶段 ${this.name} 的非关键依赖 ${dep} 未成功，但继续执行`));
				}
			}
		}

		return true;
	}

	/**
	 * 检查是否可以在依赖部分失败的情况下执行
	 * @param {Object} context
	 * @returns {boolean}
	 */
	canExecuteWithPartialFailure(context) {
		const criticalDependencies = this.getCriticalDependencies();

		// 只要关键依赖满足就可以执行
		for (const dep of criticalDependencies) {
			if (!context[dep] || context[dep].success === false) {
				return false;
			}
		}

		return true;
	}
}

module.exports = MigrationPhase;
