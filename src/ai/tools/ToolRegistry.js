/**
 * ToolRegistry - 工具注册表
 * 
 * 负责：
 * - 工具定义的注册和管理
 * - 工具描述的生成
 * - 工具验证规则
 * - 支持插件化扩展
 */
class ToolRegistry {
  constructor() {
    this.tools = new Map();
    this.categories = new Map();
    this.registerDefaultTools();
  }

  /**
   * 注册默认工具
   */
  registerDefaultTools() {
    // 文件操作工具
    this.registerTool({
      name: 'read_file',
      category: 'file',
      description: '读取项目中的文件内容',
      parameters: {
        type: 'object',
        properties: {
          file_path: {
            type: 'string',
            description: '相对于项目根目录的文件路径'
          }
        },
        required: ['file_path']
      },
      validator: (params) => this.validateFilePath(params.file_path)
    });

    this.registerTool({
      name: 'write_file',
      category: 'file',
      description: '写入或修改项目中的文件',
      parameters: {
        type: 'object',
        properties: {
          file_path: {
            type: 'string',
            description: '相对于项目根目录的文件路径'
          },
          content: {
            type: 'string',
            description: '要写入的文件内容'
          }
        },
        required: ['file_path', 'content']
      },
      validator: (params) => this.validateFilePath(params.file_path) && 
                            this.validateContent(params.content)
    });

    this.registerTool({
      name: 'list_files',
      category: 'file',
      description: '列出项目目录中的文件',
      parameters: {
        type: 'object',
        properties: {
          directory: {
            type: 'string',
            description: '要列出的目录路径，相对于项目根目录'
          },
          pattern: {
            type: 'string',
            description: '文件匹配模式，如 \'*.vue\' 或 \'*.js\''
          }
        },
        required: ['directory']
      },
      validator: (params) => this.validateDirectoryPath(params.directory)
    });

    // 命令执行工具
    this.registerTool({
      name: 'run_command',
      category: 'command',
      description: '在项目目录中执行命令行命令（如 npm install, git status 等）',
      parameters: {
        type: 'object',
        properties: {
          command: {
            type: 'string',
            description: '要执行的命令，如 "npm install" 或 "git status"'
          },
          args: {
            type: 'array',
            items: { type: 'string' },
            description: '命令参数数组（可选，如果未提供则从command中解析）'
          },
          working_directory: {
            type: 'string',
            description: '执行命令的工作目录，相对于项目根目录（可选，默认为项目根目录）'
          }
        },
        required: ['command']
      },
      validator: (params) => this.validateCommand(params.command, params.args)
    });
  }

  /**
   * 注册工具
   */
  registerTool(toolDefinition) {
    const { name, category, description, parameters, validator, executor } = toolDefinition;
    
    if (!name || !description || !parameters) {
      throw new Error('工具定义必须包含 name, description 和 parameters');
    }

    this.tools.set(name, {
      name,
      category: category || 'general',
      description,
      parameters,
      validator: validator || (() => ({ valid: true })),
      executor: executor || null,
      registeredAt: new Date()
    });

    // 按类别分组
    if (!this.categories.has(category)) {
      this.categories.set(category, []);
    }
    this.categories.get(category).push(name);
  }

  /**
   * 获取工具定义
   */
  getTool(name) {
    return this.tools.get(name);
  }

  /**
   * 获取所有工具
   */
  getAllTools() {
    return Array.from(this.tools.values());
  }

  /**
   * 获取指定类别的工具
   */
  getToolsByCategory(category) {
    const toolNames = this.categories.get(category) || [];
    return toolNames.map(name => this.tools.get(name));
  }

  /**
   * 获取工具描述（用于AI提示词）
   */
  getToolsDescription(category = null) {
    const tools = category ? this.getToolsByCategory(category) : this.getAllTools();
    return tools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n');
  }

  /**
   * 获取工具的JSON Schema格式定义
   */
  getToolsSchema() {
    return this.getAllTools().map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }));
  }

  /**
   * 验证工具调用参数
   */
  validateToolCall(toolName, parameters) {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return { valid: false, error: `未知的工具: ${toolName}` };
    }

    // 使用工具自定义的验证器
    if (tool.validator) {
      return tool.validator(parameters);
    }

    return { valid: true };
  }

  /**
   * 检查工具是否存在
   */
  hasTool(name) {
    return this.tools.has(name);
  }

  /**
   * 移除工具
   */
  unregisterTool(name) {
    const tool = this.tools.get(name);
    if (tool) {
      this.tools.delete(name);
      
      // 从类别中移除
      const category = tool.category;
      if (this.categories.has(category)) {
        const tools = this.categories.get(category);
        const index = tools.indexOf(name);
        if (index > -1) {
          tools.splice(index, 1);
        }
        
        // 如果类别为空，删除类别
        if (tools.length === 0) {
          this.categories.delete(category);
        }
      }
      
      return true;
    }
    return false;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      totalTools: this.tools.size,
      categories: Array.from(this.categories.keys()),
      toolsByCategory: Object.fromEntries(
        Array.from(this.categories.entries()).map(([cat, tools]) => [cat, tools.length])
      )
    };
  }

  // 验证方法
  validateFilePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return { valid: false, error: '文件路径不能为空' };
    }

    // 检查危险路径
    const dangerousPaths = ['../', '../', '~/', '/etc/', '/usr/', '/var/'];
    if (dangerousPaths.some(dangerous => filePath.includes(dangerous))) {
      return { valid: false, error: '不允许访问系统目录或上级目录' };
    }

    return { valid: true };
  }

  validateDirectoryPath(dirPath) {
    return this.validateFilePath(dirPath);
  }

  validateContent(content) {
    if (typeof content !== 'string') {
      return { valid: false, error: '文件内容必须是字符串' };
    }

    // 检查内容长度
    if (content.length > 1000000) { // 1MB
      return { valid: false, error: '文件内容过大（超过1MB）' };
    }

    return { valid: true };
  }

  validateCommand(command, args = []) {
    if (!command || typeof command !== 'string') {
      return { valid: false, error: '命令不能为空' };
    }

    // 允许的命令白名单
    const allowedCommands = [
      'npm', 'yarn', 'pnpm', 'cnpm',
      'node', 'npx', 'nvm',
      'git',
      'webpack', 'vite', 'rollup', 'babel',
      'gulp', 'grunt',
      'jest', 'mocha', 'vitest',
      'eslint', 'prettier', 'tslint',
      'tsc', 'typescript',
      'ls', 'dir', 'cat', 'type', 'head', 'tail',
      'mkdir', 'rmdir', 'find', 'grep',
      'cp', 'mv', 'rm', 'chmod',
      'echo', 'pwd', 'which', 'where',
      'vue'
    ];

    const cmdName = command.split(' ')[0];
    if (!allowedCommands.includes(cmdName)) {
      return { 
        valid: false, 
        error: `不允许执行的命令: ${cmdName}。允许的命令: ${allowedCommands.join(', ')}` 
      };
    }

    // 特殊命令的额外验证
    if (cmdName === 'rm') {
      // 检查是否包含危险参数
      const allArgs = Array.isArray(args) ? args : [];
      if (allArgs.includes('-rf') || allArgs.includes('-r') && allArgs.includes('-f')) {
        return { 
          valid: false, 
          error: '为安全考虑，不允许执行 rm -rf 命令' 
        };
      }
      
      // 检查是否试图删除根目录或重要系统目录
      const dangerousPaths = ['/', '/usr', '/etc', '/var', '/home', '/root'];
      for (const arg of allArgs) {
        if (dangerousPaths.includes(arg) || arg.startsWith('/')) {
          return {
            valid: false,
            error: '为安全考虑，不允许删除系统目录'
          };
        }
      }
    }

    if (cmdName === 'git' && Array.isArray(args)) {
      if (args.includes('reset') && args.includes('--hard')) {
        return { 
          valid: false, 
          error: '为安全考虑，不允许执行 git reset --hard 命令' 
        };
      }
    }

    // 检查危险参数
    const dangerousArgs = ['&&', '||', ';', '|', '>', '<', '`', '$'];
    const allArgs = Array.isArray(args) ? args : [];
    for (const arg of allArgs) {
      if (typeof arg === 'string') {
        for (const dangerous of dangerousArgs) {
          if (arg.includes(dangerous)) {
            return { 
              valid: false, 
              error: `参数中包含危险字符: ${dangerous}` 
            };
          }
        }
      }
    }

    return { valid: true };
  }
}

module.exports = ToolRegistry;
