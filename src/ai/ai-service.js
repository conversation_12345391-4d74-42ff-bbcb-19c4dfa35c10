const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const os = require('os');
require('dotenv').config();

const { createLoggingService } = require('./logging');

let generateText, createOpenAI;
let aiAvailable = false;

try {
  const ai = require('ai');
  const aiSdkOpenai = require('@ai-sdk/openai');
  generateText = ai.generateText;
  createOpenAI = aiSdkOpenai.createOpenAI;
  aiAvailable = true;
} catch (error) {
  console.warn(chalk.yellow('⚠️  AI 依赖未安装，AI 修复功能将被禁用'));
  console.warn(chalk.gray('   运行 "npm install ai @ai-sdk/openai" 来启用 AI 功能'));
  aiAvailable = false;
}

// 静态变量，用于避免重复输出警告
let aiWarningShown = false;

/**
 * Configure LLM provider based on available environment variables
 */
function configureLLMProvider() {
  // DeepSeek Provider (Prioritized)
  if (process.env.DEEPSEEK_TOKEN) {
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.DEEPSEEK_BASE_URL || "https://api.deepseek.com/v1",
      apiKey: process.env.DEEPSEEK_TOKEN,
    });

    return {
      fullModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      quickModel: process.env.DEEPSEEK_MODEL || "deepseek-chat",
      openai,
      providerName: "DeepSeek"
    };
  }

  // GLM Provider (智谱AI)
  if (process.env.GLM_API_KEY || process.env.GLM_TOKEN) {
    const apiKey = process.env.GLM_API_KEY || process.env.GLM_TOKEN;
    const openai = createOpenAI({
      compatibility: "compatible",
      baseURL: process.env.LLM_BASE_URL || "https://open.bigmodel.cn/api/paas/v4",
      apiKey: apiKey,
    });

    return {
      fullModel: process.env.LLM_MODEL || "glm-4-air",
      quickModel: process.env.LLM_MODEL || "glm-4-air",
      openai,
      providerName: "GLM"
    };
  }

  // OpenAI Provider
  if (process.env.OPENAI_API_KEY) {
    const openai = createOpenAI({
      compatibility: "strict",
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL,
    });

    return {
      fullModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      quickModel: process.env.OPENAI_MODEL || "gpt-4o-mini",
      openai,
      providerName: "OpenAI"
    };
  }

  return null;
}

/**
 * Check if any LLM provider is available
 */
function hasLLMProvider() {
  return configureLLMProvider() !== null;
}

/**
 * Get provider status for debugging
 */
function getLLMProviderStatus() {
  return {
    GLM: !!(process.env.GLM_API_KEY || process.env.GLM_TOKEN),
    DeepSeek: !!process.env.DEEPSEEK_TOKEN,
    OpenAI: !!process.env.OPENAI_API_KEY,
    Anthropic: !!process.env.ANTHROPIC_API_KEY
  };
}

/**
 * AI 服务基类
 * 提供通用的 AI 调用功能，集成完整的日志记录和统计分析
 */
class AiService {
  constructor(options = {}) {
    // 只在verbose模式下显示启动信息
    if (options.verbose) {
      console.log(chalk.blue('🚀 启动AI服务 (集成日志功能)...'));
    }

    this.options = {
      maxTokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.1,
      maxRetries: options.maxRetries || 3,
      logDir: options.logDir || path.join(process.cwd(), 'ai-logs'),
      enableRealTimeStats: options.enableRealTimeStats !== false,
      enablePerformanceTracking: options.enablePerformanceTracking !== false,
      enableErrorAnalysis: options.enableErrorAnalysis !== false,
      verbose: options.verbose || false,
      ...Object.fromEntries(
        Object.entries(options).filter(([key]) =>
          !['maxTokens', 'temperature', 'maxRetries', 'logDir', 'verbose'].includes(key)
        )
      )
    };

    // 只在verbose模式下显示日志目录
    if (this.options.verbose) {
      console.log(chalk.gray(`🔍 AIService 设置的日志目录: ${this.options.logDir}`));
    }

    // 初始化集成日志服务
    this.loggingService = createLoggingService({
      logDir: this.options.logDir,
      enableRealTimeStats: this.options.enableRealTimeStats,
      enablePerformanceTracking: this.options.enablePerformanceTracking,
      enableErrorAnalysis: this.options.enableErrorAnalysis,
      enableDebugLog: this.options.verbose || false
    });

    // 配置 LLM 提供商
    this.llmConfig = null;
    if (aiAvailable) {
      this.llmConfig = configureLLMProvider();
      if (this.llmConfig) {
        this.enabled = true;
        // 只在verbose模式下显示详细信息
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AI 服务已启用 (${this.llmConfig.providerName})`));
        }
      } else {
        this.enabled = false;
        // 只在第一次显示警告
        if (!aiWarningShown) {
          console.warn(chalk.yellow('⚠️  未找到可用的 LLM 提供商，AI 服务将被禁用'));
          console.warn(chalk.gray('   请设置以下环境变量之一:'));
          console.warn(chalk.gray('   - DEEPSEEK_TOKEN (推荐)'));
          console.warn(chalk.gray('   - GLM_API_KEY 或 GLM_TOKEN'));
          console.warn(chalk.gray('   - OPENAI_API_KEY'));
          aiWarningShown = true;
        }
      }
    } else {
      this.enabled = false;
    }

    // 当前会话记录器
    this.currentSession = null;

    // 传统统计信息（保持向后兼容）
    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 检查 AI 服务是否可用
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 调用 AI API - 集成完整的日志记录和统计功能
   */
  async callAI(prompt, options = {}) {
    if (!this.enabled) {
      throw new Error('AI 服务未启用或配置不正确');
    }

    const callOptions = {
      maxRetries: this.options.maxRetries || 3,
      ...options
    };

    // 确保 maxRetries 有效
    if (!callOptions.maxRetries || callOptions.maxRetries < 1) {
      callOptions.maxRetries = 3;
    }

    const startTime = Date.now();
    const context = callOptions.context || {};
    const { taskType = 'general', phase = 'main', attemptNumber = 1, fileName = '' } = context;

    // 更新传统统计
    this.stats.attempted++;

    // 准备日志数据
    const baseLogData = {
      timestamp: new Date().toISOString(),
      taskType,
      phase,
      attemptNumber,
      fileName,
      provider: this.llmConfig.providerName,
      model: this.llmConfig.fullModel,
      prompt: {
        length: prompt.length,
        preview: prompt
      },
      options: callOptions,
      sessionId: this.currentSession?.sessionId || null
    };

    let lastError = null;
    let result = null;

    // 记录AI调用开始
    if (this.currentSession) {
      this.currentSession.logAICall({
        ...baseLogData,
        status: 'started'
      });
    }

    // 重试循环
    for (let attempt = 1; attempt <= callOptions.maxRetries; attempt++) {
      const attemptStartTime = Date.now();

      try {
        // 只在verbose模式下显示详细的API调用信息
        if (this.options.verbose) {
          console.log(chalk.gray(
            `🤖 调用 ${this.llmConfig.providerName} API (${taskType}/${phase}, 轮次 ${attemptNumber}, 尝试 ${attempt}/${callOptions.maxRetries})...`
          ));
        }

        const { text } = await generateText({
          model: this.llmConfig.openai(this.llmConfig.fullModel),
          prompt: prompt,
          maxTokens: this.options.maxTokens,
          temperature: this.options.temperature,
        });

        const requestDuration = Date.now() - attemptStartTime;
        const totalDuration = Date.now() - startTime;

        // 处理响应 - 保持原有逻辑
        const currentTaskType = callOptions.context?.taskType || 'general';

        let hasCurrentCodeBlock
        if (currentTaskType === 'file-fix' || currentTaskType === 'error-analysis') {
          result = text.trim();
        } else {
          const codeMatch = text.match(/```[\w]*\n([\s\S]*?)\n```/);
          hasCurrentCodeBlock = !!codeMatch
          result = codeMatch ? codeMatch[1] : text.trim();
        }

        // 更新传统统计
        this.stats.success++;

        // 完整的成功日志数据
        const successLogData = {
          ...baseLogData,
          success: true,
          duration: totalDuration,
          requestDuration,
          attempts: attempt,
          response: {
            length: text.length,
            extractedCode: hasCurrentCodeBlock || currentTaskType === 'file-fix',
            returnFullResponse: currentTaskType === 'file-fix' || currentTaskType === 'error-analysis',
            preview: result.substring(0, 200) + (result.length > 200 ? '...' : '')
          },
          fullResponse: text
        };

        // 记录到集成日志服务
        await this.loggingService.logAICall(successLogData);

        // 记录到当前会话
        if (this.currentSession) {
          this.currentSession.logAICall(successLogData);
        }

        // 只在verbose模式下显示成功信息
        if (this.options.verbose) {
          console.log(chalk.green(`✅ AI 响应成功 (${requestDuration}ms)`));
        }
        return result;

      } catch (error) {
        lastError = error;
        const requestDuration = Date.now() - attemptStartTime;

        // 只在verbose模式下显示详细的失败信息
        if (this.options.verbose) {
          console.log(chalk.yellow(
            `⚠️  AI 调用失败 (${taskType}/${phase}, 轮次 ${attemptNumber}, 尝试 ${attempt}/${callOptions.maxRetries}): ${error.message}`
          ));
        }

        // 记录失败尝试
        const failureLogData = {
          ...baseLogData,
          success: false,
          duration: requestDuration,
          attempts: attempt,
          error: {
            message: error.message,
            stack: error.stack,
            type: error.constructor.name
          }
        };

        // 记录到集成日志服务
        await this.loggingService.logAICall(failureLogData);

        // 记录到当前会话
        if (this.currentSession) {
          this.currentSession.logAICall(failureLogData);
          this.currentSession.logError('ai-call-failed', error.message, {
            taskType,
            phase,
            attemptNumber,
            attempt
          });
        }

        if (attempt < callOptions.maxRetries) {
          const waitTime = 1000 * attempt;
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // 更新传统统计
    this.stats.failed++;

    // 最终失败日志
    const finalFailureLogData = {
      ...baseLogData,
      success: false,
      duration: Date.now() - startTime,
      attempts: callOptions.maxRetries,
      finalError: {
        message: lastError && lastError.message ? lastError.message : '未知错误',
        stack: lastError && lastError.stack ? lastError.stack : '无堆栈信息'
      }
    };

    await this.loggingService.logAICall(finalFailureLogData);

    const errorMessage = lastError && lastError.message ? lastError.message : '未知错误';
    throw new Error(`AI 调用失败，已重试 ${callOptions.maxRetries} 次: ${errorMessage}`);
  }

  /**
   * 写入日志文件 - 兼容原有接口，委托给日志服务
   * @deprecated 建议使用集成的日志服务
   * @private
   */
  async writeLogFile(logFilePath, logData) {
    try {
      const fileName = path.basename(logFilePath);
      const success = await this.loggingService.logManager.writeLogFile(fileName, logData);
      if (success && this.options.verbose) {
        console.log(chalk.gray(`📝 AI 调用日志已保存: ${fileName}`));
      }
      return success;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法写入日志文件: ${error.message}`));
      return false;
    }
  }

  /**
   * 备份文件
   */
  async backupFile(filePath, suffix = 'ai-backup') {
    const backupPath = `${filePath}.${suffix}`;
    await fs.copy(filePath, backupPath);
    if (this.options.verbose) {
      console.log(chalk.gray(`📁 已备份文件: ${backupPath}`));
    }

    // 记录备份操作到当前会话
    if (this.currentSession) {
      this.currentSession.logFileAction(filePath, 'backup', {
        backupPath,
        suffix,
        timestamp: new Date().toISOString()
      });
    }

    // 更新统计
    this.loggingService.statisticsCollector.stats.counters.filesBackedUp++;

    return backupPath;
  }

  /**
   * 获取统计信息 - 返回增强的统计数据
   */
  getStats() {
    const enhancedStats = this.loggingService.getStatsSummary();
    return {
      // 保持向后兼容的传统统计
      ...this.stats,
      // 增强的统计信息
      enhanced: enhancedStats
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    // 重置传统统计
    this.stats = {
      attempted: 0,
      success: 0,
      failed: 0,
      skipped: 0
    };

    // 重置增强统计
    this.loggingService.statisticsCollector.resetStats();
  }
}

module.exports = {
  AIService: AiService,
  aiAvailable
};
