const chalk = require('chalk');
const path = require('path');

/**
 * VueFileValidator - Vue 文件内容验证器
 * 
 * 负责验证修复后的 Vue 文件内容是否符合规范
 */
class VueFileValidator {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      ...options
    };
  }

  /**
   * 验证文件内容
   */
  validateContent(fixedContent, originalContent, filePath = '') {
    if (!fixedContent) {
      return {
        isValid: false,
        error: '修复内容为空',
        content: null
      };
    }

    let processedContent = fixedContent;

    // 检查是否包含 XML 标签（可能是解析错误）
    if (this.containsXmlTags(fixedContent)) {
      if (this.options.verbose) {
        console.log(chalk.gray('       🔧 检测到 XML 标签，尝试提取内容...'));
      }

      const extractedContent = this.extractFromXmlTags(fixedContent);
      if (extractedContent) {
        processedContent = extractedContent;
        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ 成功从 XML 标签中提取内容，长度: ${extractedContent.length} 字符`));
        }
      } else {
        return {
          isValid: false,
          error: '无法从 XML 标签中提取有效内容',
          content: null
        };
      }
    }

    // 检查是否是直接的 Vue 代码（AI 可能返回了不正确的格式）
    if (this.isDirectVueCode(fixedContent)) {
      if (this.options.verbose) {
        console.log(chalk.gray('       🔧 检测到直接的 Vue 代码，尝试清理格式...'));
      }

      processedContent = this.cleanDirectVueCode(fixedContent);
      if (this.options.verbose) {
        console.log(chalk.gray(`       ✅ 成功清理 Vue 代码格式，长度: ${processedContent.length} 字符`));
      }
    }

    // 标准化内容进行比较（移除多余空格和换行）
    const normalizedFixed = this.normalizeContent(processedContent);
    const normalizedOriginal = this.normalizeContent(originalContent);

    if (this.options.verbose) {
      console.log(chalk.gray(`       🔍 内容验证: 原文件${normalizedOriginal.length}字符, 修复后${normalizedFixed.length}字符`));
    }

    // 检查是否是 Vue 文件
    const isVueFile = filePath.endsWith('.vue');

    // Vue 文件特殊验证
    if (isVueFile) {
      const vueValidation = this.validateVueFileContent(normalizedFixed);
      if (!vueValidation.isValid) {
        return vueValidation;
      }
    }

    // 如果内容完全相同，检查是否是有意的"无需修复"响应
    if (normalizedFixed === normalizedOriginal) {
      return this.handleIdenticalContent(processedContent, originalContent);
    }

    // 基本的内容有效性检查
    if (normalizedFixed.length < 10) {
      return {
        isValid: false,
        error: '修复后的内容过短，可能无效',
        content: null
      };
    }

    return {
      isValid: true,
      error: null,
      content: processedContent
    };
  }

  /**
   * 验证 Vue 文件内容格式
   */
  validateVueFileContent(content) {
    // 检查是否以正确的 Vue 标签开头
    const trimmedContent = content.trim();
    
    // Vue 文件必须以 <template>、<script> 或 <style> 开头
    const validVueStarts = ['<template', '<script', '<style'];
    const startsWithValidTag = validVueStarts.some(tag => 
      trimmedContent.toLowerCase().startsWith(tag.toLowerCase())
    );

    if (!startsWithValidTag) {
      return {
        isValid: false,
        error: 'Vue 文件必须以 <template>、<script> 或 <style> 标签开头',
        content: null
      };
    }

    // 检查是否包含基本的 Vue 结构
    const hasTemplate = /<template[\s\S]*?<\/template>/i.test(content);
    const hasScript = /<script[\s\S]*?<\/script>/i.test(content);
    
    if (!hasTemplate && !hasScript) {
      return {
        isValid: false,
        error: 'Vue 文件必须包含 <template> 或 <script> 标签',
        content: null
      };
    }

    return {
      isValid: true,
      error: null,
      content: content
    };
  }

  /**
   * 处理内容相同的情况
   */
  handleIdenticalContent(fixedContent, originalContent) {
    // 检查响应中是否包含"无需修复"的指示
    const noFixIndicators = [
      '无需修复',
      'no fix needed',
      'already correct',
      '已经正确',
      'file is correct'
    ];

    // 如果 AI 明确表示无需修复，则返回原内容（表示处理成功）
    // 否则返回 null（表示修复失败）
    const hasNoFixIndicator = noFixIndicators.some(indicator =>
      fixedContent.toLowerCase().includes(indicator.toLowerCase())
    );

    if (hasNoFixIndicator) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ℹ️  AI 判断文件无需修复'));
      }
      return {
        isValid: true,
        error: null,
        content: originalContent
      };
    } else {
      if (this.options.verbose) {
        console.log(chalk.gray('       ⚠️  AI 返回的内容与原文件相同，可能修复失败'));
      }
      return {
        isValid: false,
        error: 'AI 返回的内容与原文件相同，可能修复失败',
        content: null
      };
    }
  }

  /**
   * 标准化内容
   */
  normalizeContent(content) {
    return content
      .replace(/\r\n/g, '\n') // 统一换行符
      .replace(/\s+$/gm, '') // 移除行尾空格
      .trim(); // 移除首尾空格
  }

  /**
   * 检查内容是否包含 XML 标签（可能是解析错误）
   */
  containsXmlTags(content) {
    const xmlTags = ['<fix_result>', '<fixed_content>', '<changes_made>', '<file_fix>'];
    return xmlTags.some(tag => content.includes(tag));
  }

  /**
   * 从 XML 标签中提取内容
   */
  extractFromXmlTags(content) {
    // 尝试从 <fixed_content> 标签中提取内容
    const fixedContentMatch = content.match(/<fixed_content>([\s\S]*?)<\/fixed_content>/);
    if (fixedContentMatch) {
      return fixedContentMatch[1].trim();
    }

    // 尝试从 <content> 标签中提取内容
    const contentMatch = content.match(/<content>([\s\S]*?)<\/content>/);
    if (contentMatch) {
      return contentMatch[1].trim();
    }

    return null;
  }

  /**
   * 检查是否是直接的 Vue 代码（没有 XML 包装）
   */
  isDirectVueCode(content) {
    const trimmedContent = content.trim();

    // 检查是否以语言标识符开头（如 "vue"）
    if (trimmedContent.startsWith('vue\n') || trimmedContent.startsWith('vue ')) {
      return true;
    }

    // 检查是否直接以 Vue 标签开头
    const vueTagPattern = /^\s*<(template|script|style)/;
    return vueTagPattern.test(trimmedContent);
  }

  /**
   * 清理直接的 Vue 代码格式
   */
  cleanDirectVueCode(content) {
    let cleanedContent = content.trim();

    // 如果以语言标识符开头，移除它
    if (cleanedContent.startsWith('vue\n')) {
      cleanedContent = cleanedContent.substring(4).trim();
    } else if (cleanedContent.startsWith('vue ')) {
      cleanedContent = cleanedContent.substring(4).trim();
    }

    // 移除可能的尾部标记（如 </changes_made>）
    const endTagPattern = /<\/?\w+>\s*$/;
    if (endTagPattern.test(cleanedContent)) {
      cleanedContent = cleanedContent.replace(endTagPattern, '').trim();
    }

    return cleanedContent;
  }
}

module.exports = VueFileValidator;
