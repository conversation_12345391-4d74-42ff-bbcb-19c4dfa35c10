const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 统计信息收集器 - 专门收集和分析AI修复过程中的各种统计数据
 * 提供性能指标、成功率分析、错误模式识别等功能
 */
class StatisticsCollector {
  constructor(options = {}) {
    this.options = {
      enableRealTimeStats: options.enableRealTimeStats || true,
      enablePerformanceTracking: options.enablePerformanceTracking || true,
      enableErrorAnalysis: options.enableErrorAnalysis || true,
      verbose: options.verbose || false,
      ...options
    };

    // 实时统计数据
    this.stats = {
      // 基础计数
      counters: {
        aiCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        filesAnalyzed: 0,
        filesModified: 0,
        filesBackedUp: 0,
        errorsFixed: 0,
        attemptsTotal: 0,
        sessionsTotal: 0
      },

      // 性能指标
      performance: {
        totalDuration: 0,
        averageCallDuration: 0,
        fastestCall: null,
        slowestCall: null,
        callDurations: [],
        memoryUsage: [],
        timestamps: []
      },

      // 错误分析
      errors: {
        byType: {},
        byPhase: {},
        byAttempt: {},
        patterns: [],
        recurring: []
      },

      // 成功率分析
      successRates: {
        overall: 0,
        byTaskType: {},
        byPhase: {},
        byAttempt: {},
        trends: []
      },

      // 文件类型分析
      fileTypes: {
        processed: {},
        successful: {},
        failed: {}
      },

      // AI提供商性能
      providers: {
        usage: {},
        performance: {},
        reliability: {}
      }
    };

    // 历史数据缓存
    this.history = {
      sessions: [],
      dailyStats: {},
      weeklyStats: {},
      trends: []
    };

    this.startTime = Date.now();
  }

  /**
   * 记录AI调用
   * @param {Object} callData - AI调用数据
   */
  recordAICall(callData) {
    const {
      success,
      duration,
      taskType,
      phase,
      attemptNumber,
      provider,
      errorType,
      fileName
    } = callData;

    // 更新基础计数
    this.stats.counters.aiCalls++;
    if (success) {
      this.stats.counters.successfulCalls++;
    } else {
      this.stats.counters.failedCalls++;
    }

    // 更新性能指标
    if (duration && this.options.enablePerformanceTracking) {
      this._updatePerformanceStats(duration, callData);
    }

    // 更新错误分析
    if (!success && this.options.enableErrorAnalysis) {
      this._updateErrorStats(errorType, phase, attemptNumber, callData);
    }

    // 更新成功率
    this._updateSuccessRates(success, taskType, phase, attemptNumber);

    // 更新文件类型统计
    if (fileName) {
      this._updateFileTypeStats(fileName, success);
    }

    // 更新提供商统计
    if (provider) {
      this._updateProviderStats(provider, success, duration);
    }

    // 记录时间戳用于趋势分析
    this.stats.performance.timestamps.push({
      timestamp: Date.now(),
      success,
      duration,
      taskType,
      phase
    });

    // 实时显示统计（如果启用且在verbose模式下）
    if (this.options.enableRealTimeStats && this.options.verbose && this.stats.counters.aiCalls % 5 === 0) {
      this._displayRealTimeStats();
    }
  }

  /**
   * 更新性能统计
   * @private
   */
  _updatePerformanceStats(duration, callData) {
    const perf = this.stats.performance;
    
    perf.callDurations.push(duration);
    perf.totalDuration += duration;
    
    // 更新平均值
    perf.averageCallDuration = perf.totalDuration / this.stats.counters.aiCalls;
    
    // 更新最快/最慢记录
    if (!perf.fastestCall || duration < perf.fastestCall.duration) {
      perf.fastestCall = { duration, ...callData };
    }
    
    if (!perf.slowestCall || duration > perf.slowestCall.duration) {
      perf.slowestCall = { duration, ...callData };
    }

    // 限制数组大小，保留最近1000次调用
    if (perf.callDurations.length > 1000) {
      perf.callDurations = perf.callDurations.slice(-1000);
    }

    // 记录内存使用情况
    if (perf.memoryUsage.length % 10 === 0) { // 每10次记录一次内存
      const memUsage = process.memoryUsage();
      perf.memoryUsage.push({
        timestamp: Date.now(),
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      });
      
      // 限制内存记录数量
      if (perf.memoryUsage.length > 100) {
        perf.memoryUsage = perf.memoryUsage.slice(-100);
      }
    }
  }

  /**
   * 更新错误统计
   * @private
   */
  _updateErrorStats(errorType, phase, attemptNumber, callData) {
    const errors = this.stats.errors;
    
    // 按类型统计
    if (errorType) {
      errors.byType[errorType] = (errors.byType[errorType] || 0) + 1;
    }
    
    // 按阶段统计
    if (phase) {
      errors.byPhase[phase] = (errors.byPhase[phase] || 0) + 1;
    }
    
    // 按尝试次数统计
    if (attemptNumber) {
      errors.byAttempt[attemptNumber] = (errors.byAttempt[attemptNumber] || 0) + 1;
    }

    // 错误模式识别
    this._analyzeErrorPatterns(callData);
  }

  /**
   * 分析错误模式
   * @private
   */
  _analyzeErrorPatterns(callData) {
    const { errorType, phase, fileName, message } = callData;
    
    // 寻找重复错误
    const errorKey = `${errorType}-${phase}-${fileName}`;
    const existing = this.stats.errors.recurring.find(r => r.key === errorKey);
    
    if (existing) {
      existing.count++;
      existing.lastSeen = Date.now();
    } else {
      this.stats.errors.recurring.push({
        key: errorKey,
        errorType,
        phase,
        fileName,
        count: 1,
        firstSeen: Date.now(),
        lastSeen: Date.now(),
        message
      });
    }

    // 限制重复错误记录数量
    if (this.stats.errors.recurring.length > 50) {
      this.stats.errors.recurring.sort((a, b) => b.count - a.count);
      this.stats.errors.recurring = this.stats.errors.recurring.slice(0, 50);
    }
  }

  /**
   * 更新成功率统计
   * @private
   */
  _updateSuccessRates(success, taskType, phase, attemptNumber) {
    const rates = this.stats.successRates;
    
    // 整体成功率
    rates.overall = this.stats.counters.successfulCalls / this.stats.counters.aiCalls;
    
    // 按任务类型
    if (taskType) {
      if (!rates.byTaskType[taskType]) {
        rates.byTaskType[taskType] = { total: 0, successful: 0, rate: 0 };
      }
      rates.byTaskType[taskType].total++;
      if (success) rates.byTaskType[taskType].successful++;
      rates.byTaskType[taskType].rate = rates.byTaskType[taskType].successful / rates.byTaskType[taskType].total;
    }
    
    // 按阶段
    if (phase) {
      if (!rates.byPhase[phase]) {
        rates.byPhase[phase] = { total: 0, successful: 0, rate: 0 };
      }
      rates.byPhase[phase].total++;
      if (success) rates.byPhase[phase].successful++;
      rates.byPhase[phase].rate = rates.byPhase[phase].successful / rates.byPhase[phase].total;
    }
    
    // 按尝试次数
    if (attemptNumber) {
      if (!rates.byAttempt[attemptNumber]) {
        rates.byAttempt[attemptNumber] = { total: 0, successful: 0, rate: 0 };
      }
      rates.byAttempt[attemptNumber].total++;
      if (success) rates.byAttempt[attemptNumber].successful++;
      rates.byAttempt[attemptNumber].rate = rates.byAttempt[attemptNumber].successful / rates.byAttempt[attemptNumber].total;
    }
  }

  /**
   * 更新文件类型统计
   * @private
   */
  _updateFileTypeStats(fileName, success) {
    const ext = path.extname(fileName).toLowerCase() || 'no-ext';
    const fileTypes = this.stats.fileTypes;
    
    // 初始化
    if (!fileTypes.processed[ext]) {
      fileTypes.processed[ext] = 0;
      fileTypes.successful[ext] = 0;
      fileTypes.failed[ext] = 0;
    }
    
    fileTypes.processed[ext]++;
    if (success) {
      fileTypes.successful[ext]++;
    } else {
      fileTypes.failed[ext]++;
    }
  }

  /**
   * 更新AI提供商统计
   * @private
   */
  _updateProviderStats(provider, success, duration) {
    const providers = this.stats.providers;
    
    // 使用统计
    if (!providers.usage[provider]) {
      providers.usage[provider] = { total: 0, successful: 0, failed: 0 };
    }
    providers.usage[provider].total++;
    if (success) {
      providers.usage[provider].successful++;
    } else {
      providers.usage[provider].failed++;
    }
    
    // 性能统计
    if (duration) {
      if (!providers.performance[provider]) {
        providers.performance[provider] = { 
          totalDuration: 0, 
          averageDuration: 0, 
          callCount: 0,
          fastest: null,
          slowest: null
        };
      }
      
      const perf = providers.performance[provider];
      perf.totalDuration += duration;
      perf.callCount++;
      perf.averageDuration = perf.totalDuration / perf.callCount;
      
      if (!perf.fastest || duration < perf.fastest) {
        perf.fastest = duration;
      }
      if (!perf.slowest || duration > perf.slowest) {
        perf.slowest = duration;
      }
    }
    
    // 可靠性统计
    if (!providers.reliability[provider]) {
      providers.reliability[provider] = { 
        successRate: 0,
        uptime: 100,
        lastFailure: null
      };
    }
    
    providers.reliability[provider].successRate = 
      providers.usage[provider].successful / providers.usage[provider].total;
    
    if (!success) {
      providers.reliability[provider].lastFailure = Date.now();
    }
  }

  /**
   * 实时显示统计信息
   * @private
   */
  _displayRealTimeStats() {
    const { counters, performance, successRates } = this.stats;
    const uptime = Math.round((Date.now() - this.startTime) / 1000);
    
    console.log(chalk.blue(`📊 实时统计 (运行时间: ${uptime}s)`));
    console.log(chalk.gray(`   AI调用: ${counters.aiCalls} | 成功: ${counters.successfulCalls} | 失败: ${counters.failedCalls}`));
    console.log(chalk.gray(`   成功率: ${Math.round(successRates.overall * 100)}% | 平均耗时: ${Math.round(performance.averageCallDuration)}ms`));
    console.log(chalk.gray(`   文件处理: ${counters.filesAnalyzed} 分析 | ${counters.filesModified} 修改`));
  }

  /**
   * 记录会话开始
   * @param {string} sessionId - 会话ID
   * @param {Object} metadata - 会话元数据
   */
  recordSessionStart(sessionId, metadata = {}) {
    this.stats.counters.sessionsTotal++;
    
    const session = {
      sessionId,
      startTime: Date.now(),
      endTime: null,
      metadata,
      stats: {
        aiCalls: 0,
        filesProcessed: 0,
        errors: 0,
        success: null
      }
    };
    
    this.history.sessions.push(session);
    return session;
  }

  /**
   * 记录会话结束
   * @param {string} sessionId - 会话ID
   * @param {boolean} success - 会话是否成功
   * @param {Object} finalStats - 最终统计数据
   */
  recordSessionEnd(sessionId, success, finalStats = {}) {
    const session = this.history.sessions.find(s => s.sessionId === sessionId);
    if (session) {
      session.endTime = Date.now();
      session.stats = { ...session.stats, ...finalStats };
      session.success = success;
      session.duration = session.endTime - session.startTime;
    }
  }

  /**
   * 获取统计摘要
   * @returns {Object} 统计摘要
   */
  getStatsSummary() {
    const uptime = Date.now() - this.startTime;
    const { counters, performance, successRates, errors } = this.stats;
    
    return {
      overview: {
        uptime: Math.round(uptime / 1000),
        totalCalls: counters.aiCalls,
        successRate: Math.round(successRates.overall * 100),
        averageDuration: Math.round(performance.averageCallDuration),
        filesProcessed: counters.filesAnalyzed,
        filesModified: counters.filesModified,
        totalSessions: counters.sessionsTotal
      },
      performance: {
        fastest: performance.fastestCall?.duration || 0,
        slowest: performance.slowestCall?.duration || 0,
        totalDuration: Math.round(performance.totalDuration / 1000),
        memoryUsed: this._getCurrentMemoryUsage()
      },
      errors: {
        total: counters.failedCalls,
        mostCommon: this._getMostCommonErrors(3),
        recurring: this.stats.errors.recurring.filter(r => r.count > 2).length
      },
      providers: this._getProviderSummary(),
      fileTypes: this._getFileTypeSummary()
    };
  }

  /**
   * 获取当前内存使用情况
   * @private
   */
  _getCurrentMemoryUsage() {
    const mem = process.memoryUsage();
    return {
      heapUsed: Math.round(mem.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(mem.heapTotal / 1024 / 1024), // MB
      external: Math.round(mem.external / 1024 / 1024) // MB
    };
  }

  /**
   * 获取最常见的错误
   * @private
   */
  _getMostCommonErrors(limit = 5) {
    return Object.entries(this.stats.errors.byType)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([type, count]) => ({ type, count }));
  }

  /**
   * 获取提供商摘要
   * @private
   */
  _getProviderSummary() {
    const summary = {};
    
    Object.entries(this.stats.providers.usage).forEach(([provider, usage]) => {
      const perf = this.stats.providers.performance[provider] || {};
      const reliability = this.stats.providers.reliability[provider] || {};
      
      summary[provider] = {
        calls: usage.total,
        successRate: Math.round((usage.successful / usage.total) * 100),
        averageDuration: Math.round(perf.averageDuration || 0),
        reliability: Math.round(reliability.successRate * 100)
      };
    });
    
    return summary;
  }

  /**
   * 获取文件类型摘要
   * @private
   */
  _getFileTypeSummary() {
    const summary = {};
    
    Object.entries(this.stats.fileTypes.processed).forEach(([ext, total]) => {
      const successful = this.stats.fileTypes.successful[ext] || 0;
      summary[ext] = {
        processed: total,
        successful,
        successRate: Math.round((successful / total) * 100)
      };
    });
    
    return summary;
  }

  /**
   * 生成详细报告
   * @returns {Object} 详细统计报告
   */
  generateDetailedReport() {
    return {
      reportType: 'detailed-statistics',
      generatedAt: new Date().toISOString(),
      summary: this.getStatsSummary(),
      rawStats: { ...this.stats },
      history: {
        sessions: this.history.sessions.slice(-10), // 最近10个会话
        trends: this._generateTrends()
      },
      recommendations: this._generateRecommendations()
    };
  }

  /**
   * 生成趋势分析
   * @private
   */
  _generateTrends() {
    const timestamps = this.stats.performance.timestamps.slice(-100); // 最近100次调用
    if (timestamps.length < 10) return [];
    
    const trends = [];
    
    // 成功率趋势
    const recentSuccess = timestamps.slice(-20).filter(t => t.success).length / 20;
    const earlierSuccess = timestamps.slice(-40, -20).filter(t => t.success).length / 20;
    
    if (recentSuccess > earlierSuccess + 0.1) {
      trends.push({ type: 'success-rate', direction: 'improving', value: recentSuccess });
    } else if (recentSuccess < earlierSuccess - 0.1) {
      trends.push({ type: 'success-rate', direction: 'declining', value: recentSuccess });
    }
    
    // 性能趋势
    const recentAvgDuration = timestamps.slice(-20).reduce((sum, t) => sum + (t.duration || 0), 0) / 20;
    const earlierAvgDuration = timestamps.slice(-40, -20).reduce((sum, t) => sum + (t.duration || 0), 0) / 20;
    
    if (recentAvgDuration < earlierAvgDuration * 0.9) {
      trends.push({ type: 'performance', direction: 'improving', value: recentAvgDuration });
    } else if (recentAvgDuration > earlierAvgDuration * 1.1) {
      trends.push({ type: 'performance', direction: 'declining', value: recentAvgDuration });
    }
    
    return trends;
  }

  /**
   * 生成建议
   * @private
   */
  _generateRecommendations() {
    const recommendations = [];
    const summary = this.getStatsSummary();
    
    // 成功率建议
    if (summary.overview.successRate < 70) {
      recommendations.push({
        type: 'success-rate',
        priority: 'high',
        message: '成功率较低，建议检查提示词质量和错误处理逻辑'
      });
    }
    
    // 性能建议
    if (summary.performance.slowest > 30000) { // 30秒
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: '存在响应时间过长的调用，建议优化提示词长度或模型配置'
      });
    }
    
    // 错误建议
    if (summary.errors.recurring > 5) {
      recommendations.push({
        type: 'errors',
        priority: 'medium',
        message: '存在多个重复错误，建议分析错误模式并优化修复策略'
      });
    }
    
    return recommendations;
  }

  /**
   * 重置统计数据
   */
  resetStats() {
    this.stats = {
      counters: {
        aiCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        filesAnalyzed: 0,
        filesModified: 0,
        filesBackedUp: 0,
        errorsFixed: 0,
        attemptsTotal: 0,
        sessionsTotal: 0
      },
      performance: {
        totalDuration: 0,
        averageCallDuration: 0,
        fastestCall: null,
        slowestCall: null,
        callDurations: [],
        memoryUsage: [],
        timestamps: []
      },
      errors: {
        byType: {},
        byPhase: {},
        byAttempt: {},
        patterns: [],
        recurring: []
      },
      successRates: {
        overall: 0,
        byTaskType: {},
        byPhase: {},
        byAttempt: {},
        trends: []
      },
      fileTypes: {
        processed: {},
        successful: {},
        failed: {}
      },
      providers: {
        usage: {},
        performance: {},
        reliability: {}
      }
    };
    
    this.startTime = Date.now();
    if (this.options.verbose) {
      console.log(chalk.blue('📊 统计数据已重置'));
    }
  }
}

module.exports = StatisticsCollector;
