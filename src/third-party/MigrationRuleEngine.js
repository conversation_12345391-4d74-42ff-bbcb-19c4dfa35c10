const chalk = require('chalk');

/**
 * MigrationRuleEngine - 迁移规则引擎
 *
 * 功能：
 * - 提供基于规则的自动迁移
 * - 处理简单的import替换和API调整
 * - 减少AI调用成本
 * - 提供确定性的迁移结果
 */
class MigrationRuleEngine {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      ...options
    };

    // 迁移规则定义
    this.rules = this.loadMigrationRules();
  }

  /**
   * 加载迁移规则
   */
  loadMigrationRules() {
    return {
      'vue-count-to': {
        importRules: [
          {
            from: /import\s+.*\s+from\s+['"]vue-count-to['"]/g,
            to: 'import CountTo from "vue3-count-to"'
          },
          {
            from: /const\s+.*\s+=\s+require\(['"]vue-count-to['"]\)/g,
            to: 'import CountTo from "vue3-count-to"'
          }
        ],
        componentRules: [
          {
            from: /countTo/g,
            to: 'CountTo'
          }
        ],
        apiRules: []
      },
      'vue-splitpane': {
        importRules: [
          {
            from: /import\s+.*\s+from\s+['"]vue-splitpane['"]/g,
            to: 'import { Splitpanes, Pane } from "splitpanes"'
          }
        ],
        componentRules: [
          {
            from: /<split-pane/g,
            to: '<splitpanes'
          },
          {
            from: /<\/split-pane>/g,
            to: '</splitpanes>'
          },
          {
            from: /<pane/g,
            to: '<pane'
          }
        ],
        apiRules: []
      },
      'vue-uuid': {
        importRules: [
          {
            from: /import\s+.*\s+from\s+['"]vue-uuid['"]/g,
            to: 'import UUID from "vue3-uuid"'
          }
        ],
        componentRules: [],
        apiRules: [
          {
            from: /this\.\$uuid/g,
            to: 'this.$uuid'
          }
        ]
      },
      'vue-template-compiler': {
        importRules: [
          {
            from: /import\s+.*\s+from\s+['"]vue-template-compiler['"]/g,
            to: 'import * as compiler from "@vue/compiler-sfc"'
          },
          {
            from: /const\s+.*\s+=\s+require\(['"]vue-template-compiler['"]\)/g,
            to: 'import * as compiler from "@vue/compiler-sfc"'
          }
        ],
        componentRules: [],
        apiRules: [
          {
            from: /\.compile\(/g,
            to: '.compileTemplate('
          }
        ]
      },
      'vue-json-pretty': {
        importRules: [
          {
            from: /import\s+.*\s+from\s+['"]vue-json-pretty['"]/g,
            to: 'import VueJsonPretty from "vue-json-pretty"'
          }
        ],
        componentRules: [],
        apiRules: []
      }
    };
  }

  /**
   * 应用迁移规则
   */
  applyRules(content, component) {
    const componentName = component.name;
    const rules = this.rules[componentName];

    if (!rules) {
      return {
        success: true,
        hasChanges: false,
        content: content,
        appliedRules: []
      };
    }

    let modifiedContent = content;
    const appliedRules = [];

    try {
      // 应用import规则
      for (const rule of rules.importRules) {
        const matches = modifiedContent.match(rule.from);
        if (matches) {
          modifiedContent = modifiedContent.replace(rule.from, rule.to);
          appliedRules.push({
            type: 'import',
            description: `替换import语句: ${matches[0]} -> ${rule.to}`
          });
        }
      }

      // 应用组件规则
      for (const rule of rules.componentRules) {
        const matches = modifiedContent.match(rule.from);
        if (matches) {
          modifiedContent = modifiedContent.replace(rule.from, rule.to);
          appliedRules.push({
            type: 'component',
            description: `替换组件使用: ${rule.from} -> ${rule.to}`
          });
        }
      }

      // 应用API规则
      for (const rule of rules.apiRules) {
        const matches = modifiedContent.match(rule.from);
        if (matches) {
          modifiedContent = modifiedContent.replace(rule.from, rule.to);
          appliedRules.push({
            type: 'api',
            description: `替换API调用: ${rule.from} -> ${rule.to}`
          });
        }
      }

      const hasChanges = appliedRules.length > 0;

      if (this.options.verbose && hasChanges) {
        console.log(chalk.gray(`    🔧 规则引擎应用了 ${appliedRules.length} 个规则`));
        appliedRules.forEach(rule => {
          console.log(chalk.gray(`      - ${rule.description}`));
        });
      }

      return {
        success: true,
        hasChanges: hasChanges,
        content: modifiedContent,
        appliedRules: appliedRules
      };
    } catch (error) {
      return {
        success: false,
        hasChanges: false,
        content: content,
        error: error.message,
        appliedRules: []
      };
    }
  }

  /**
   * 检查是否可以完全通过规则处理
   */
  canHandleWithRules(content, component) {
    const componentName = component.name;
    const rules = this.rules[componentName];

    if (!rules) {
      return false;
    }

    // 检查是否包含复杂的使用模式
    const complexPatterns = [
      /\$refs\./,           // 使用refs
      /\$emit\(/,           // 事件发射
      /\$on\(/,             // 事件监听
      /\$nextTick/,         // nextTick
      /mixins:/,            // mixins
      /extends:/,           // extends
      /render\s*\(/,        // render函数
      /createElement/,      // createElement
      /h\(/,                // h函数
      /slots\./,            // slots
      /scopedSlots/         // scoped slots
    ];

    const hasComplexUsage = complexPatterns.some(pattern => pattern.test(content));

    if (hasComplexUsage) {
      return false;
    }

    // 检查是否只包含简单的import和基本使用
    const simplePatterns = [
      /import.*from/,       // import语句
      /require\(/,          // require语句
      /<[a-zA-Z-]+/,        // 组件标签
      /components:/         // 组件注册
    ];

    const hasSimpleUsage = simplePatterns.some(pattern => pattern.test(content));

    return hasSimpleUsage;
  }

  /**
   * 获取组件的迁移复杂度评估
   */
  getComplexityAssessment(content, component) {
    const componentName = component.name;
    const rules = this.rules[componentName];

    if (!rules) {
      return {
        complexity: 'unknown',
        canUseRules: false,
        recommendedApproach: 'ai'
      };
    }

    const canUseRules = this.canHandleWithRules(content, component);
    const lineCount = content.split('\n').length;

    let complexity = 'simple';
    let recommendedApproach = 'rules';

    if (lineCount > 200) {
      complexity = 'complex';
      recommendedApproach = canUseRules ? 'rules' : 'ai-two-phase';
    } else if (lineCount > 50 || !canUseRules) {
      complexity = 'medium';
      recommendedApproach = canUseRules ? 'rules' : 'ai-direct';
    }

    return {
      complexity,
      canUseRules,
      recommendedApproach,
      lineCount
    };
  }

  /**
   * 添加自定义规则
   */
  addCustomRule(componentName, ruleType, rule) {
    if (!this.rules[componentName]) {
      this.rules[componentName] = {
        importRules: [],
        componentRules: [],
        apiRules: []
      };
    }

    const ruleTypeKey = `${ruleType}Rules`;
    if (this.rules[componentName][ruleTypeKey]) {
      this.rules[componentName][ruleTypeKey].push(rule);
    }
  }

  /**
   * 获取支持的组件列表
   */
  getSupportedComponents() {
    return Object.keys(this.rules);
  }

  /**
   * 获取组件的规则统计
   */
  getRuleStats(componentName) {
    const rules = this.rules[componentName];
    if (!rules) {
      return null;
    }

    return {
      component: componentName,
      importRules: rules.importRules.length,
      componentRules: rules.componentRules.length,
      apiRules: rules.apiRules.length,
      totalRules: rules.importRules.length + rules.componentRules.length + rules.apiRules.length
    };
  }

  /**
   * 验证规则的有效性
   */
  validateRules() {
    const issues = [];

    for (const [componentName, rules] of Object.entries(this.rules)) {
      // 检查规则格式
      ['importRules', 'componentRules', 'apiRules'].forEach(ruleType => {
        if (!Array.isArray(rules[ruleType])) {
          issues.push(`${componentName}.${ruleType} 不是数组`);
          return;
        }

        rules[ruleType].forEach((rule, index) => {
          if (!rule.from || !rule.to) {
            issues.push(`${componentName}.${ruleType}[${index}] 缺少from或to字段`);
          }

          if (!(rule.from instanceof RegExp) && typeof rule.from !== 'string') {
            issues.push(`${componentName}.${ruleType}[${index}].from 必须是RegExp或字符串`);
          }
        });
      });
    }

    return {
      valid: issues.length === 0,
      issues: issues
    };
  }
}

module.exports = MigrationRuleEngine;
