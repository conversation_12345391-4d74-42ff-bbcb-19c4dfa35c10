# Vue 2 到 Vue 3 第三方组件迁移 Agent

这是一个基于 AI Agent 模式的第三方组件自动化迁移工具，专门用于处理 Vue 2 到 Vue 3 项目中不兼容的第三方组件迁移。

## 🚀 核心特性

### 智能迁移策略
- **规则引擎优先**：对于简单的import替换和API调整，使用确定性规则引擎，节省AI成本
- **小文件直接AI翻译**：200行以内的文件直接使用AI进行完整翻译
- **大文件两阶段AI处理**：超过200行的文件使用两阶段AI模式，先分析后生成

### 高效搜索
- **Ripgrep集成**：使用高性能的ripgrep进行文件搜索（可选）
- **智能组件识别**：基于package.json和代码模式识别需要迁移的组件
- **精确定位**：准确找到组件的使用位置和上下文

### AI Agent 架构
- **工具化设计**：参考Augment的工具系统，提供read_file、write_file等工具
- **两阶段AI调用**：第一阶段分析，第二阶段生成，提高准确性
- **成本优化**：避免不必要的AI调用，优先使用规则引擎

## 📦 支持的组件

当前支持以下第三方组件的自动迁移：

| Vue 2 组件 | Vue 3 目标 | 迁移方式 |
|-----------|-----------|---------|
| vue-count-to | vue3-count-to | 规则+AI |
| vuedraggable | vue.draggable.next | 规则+AI |
| vue-splitpane | splitpanes | 规则+AI |
| @riophae/vue-treeselect | Element Plus tree-select | AI |
| v-charts | vue-echarts | 规则+AI |
| vue-scrollbars | vue3-perfect-scrollbar | 规则+AI |
| vue-uuid | vue3-uuid | 规则+AI |
| @tinymce/tinymce-vue | @tinymce/tinymce-vue@^4 | 规则+AI |
| vue-template-compiler | @vue/compiler-sfc | 规则+AI |
| vue-json-pretty | vue-json-pretty@^2 | 规则+AI |
| vue2-tree-org | vue3-tree-org | 规则+AI |

## 🛠️ 使用方法

### 命令行使用

```bash
# 执行完整迁移
node bin/third-party-migrator.js migrate /path/to/your/vue2-project

# 预览模式（不实际修改文件）
node bin/third-party-migrator.js migrate /path/to/your/vue2-project --dry-run

# 详细输出
node bin/third-party-migrator.js migrate /path/to/your/vue2-project --verbose

# 分析项目中的第三方组件使用情况
node bin/third-party-migrator.js analyze /path/to/your/vue2-project

# 测试规则引擎
node bin/third-party-migrator.js test-rules /path/to/test-file.vue --component vue-count-to

# 查看支持的组件列表
node bin/third-party-migrator.js help-components
```

### 编程式使用

```javascript
const ThirdPartyMigrationAgent = require('./src/third-party/ThirdPartyMigrationAgent');

const agent = new ThirdPartyMigrationAgent('/path/to/project', {
  maxFileSize: 200,        // 小文件直接AI翻译的最大行数
  maxAttempts: 3,          // 最大尝试次数
  dryRun: false,           // 是否为预览模式
  verbose: true,           // 详细输出
  useRipgrep: true,        // 使用ripgrep搜索
  apiKey: 'your-ai-api-key' // AI API密钥
});

const result = await agent.migrate();
console.log('迁移结果:', result);
```

## 🏗️ 架构设计

### 核心组件

1. **ThirdPartyMigrationAgent** - 主控制器
   - 继承自AIService，提供AI调用能力
   - 协调各个组件完成迁移流程
   - 实现两阶段AI调用模式

2. **ComponentSearcher** - 组件搜索器
   - 使用ripgrep或glob进行高效搜索
   - 识别项目中的第三方组件使用
   - 提供结构化的搜索结果

3. **MigrationRuleEngine** - 迁移规则引擎
   - 提供基于规则的确定性迁移
   - 处理简单的import替换和API调整
   - 减少AI调用成本

4. **ToolExecutor** - 工具执行器
   - 提供文件读写、命令执行等工具
   - 支持dry-run模式
   - 安全的工具调用管理

### 迁移流程

```mermaid
graph TD
    A[开始迁移] --> B[分析项目依赖]
    B --> C[搜索组件使用]
    C --> D{文件大小判断}
    D -->|小文件| E[尝试规则引擎]
    D -->|大文件| F[两阶段AI处理]
    E --> G{规则引擎成功?}
    G -->|是| H[应用规则修改]
    G -->|否| I[AI直接翻译]
    F --> J[AI分析阶段]
    J --> K[AI生成阶段]
    H --> L[写入文件]
    I --> L
    K --> L
    L --> M[生成报告]
    M --> N[完成]
```

## 🔧 配置选项

### Agent 选项

```javascript
{
  maxFileSize: 200,        // 小文件阈值（行数）
  maxAttempts: 3,          // 最大重试次数
  dryRun: false,           // 预览模式
  verbose: false,          // 详细输出
  useRipgrep: true,        // 使用ripgrep搜索
  apiKey: undefined        // AI API密钥
}
```

### 搜索选项

```javascript
{
  useRipgrep: true,        // 使用ripgrep
  verbose: false,          // 详细输出
  includePatterns: [       // 包含的文件模式
    '**/*.vue',
    '**/*.js',
    '**/*.ts',
    '**/*.jsx',
    '**/*.tsx'
  ],
  excludePatterns: [       // 排除的文件模式
    'node_modules/**',
    'dist/**',
    'build/**',
    '**/*.test.*',
    '**/*.spec.*'
  ]
}
```

## 📊 迁移报告

工具会生成详细的迁移报告：

```
📊 第三方组件迁移报告:
总文件数: 15
处理文件数: 12
AI迁移: 8
规则迁移: 4
跳过文件: 3
错误数: 0
```

## 🧪 测试

运行测试套件：

```bash
npm test -- test/third-party-migration.test.js
```

测试覆盖：
- ComponentSearcher 功能测试
- MigrationRuleEngine 规则测试
- ThirdPartyMigrationAgent 集成测试
- 错误处理测试

## 🔄 扩展支持

### 添加新组件支持

1. 在 `ComponentSearcher.js` 中添加组件映射：

```javascript
'new-component': {
  target: 'new-component-vue3',
  patterns: ['new-component', 'NewComponent'],
  migrationGuide: 'Migration instructions'
}
```

2. 在 `MigrationRuleEngine.js` 中添加迁移规则：

```javascript
'new-component': {
  importRules: [
    {
      from: /import.*from\s+['"]new-component['"]/g,
      to: 'import NewComponent from "new-component-vue3"'
    }
  ],
  componentRules: [],
  apiRules: []
}
```

### 自定义规则

```javascript
const ruleEngine = new MigrationRuleEngine();
ruleEngine.addCustomRule('my-component', 'import', {
  from: /old-pattern/g,
  to: 'new-pattern'
});
```

## 🚨 注意事项

1. **备份重要**：工具会自动创建 `.backup` 文件，但建议使用版本控制
2. **AI API密钥**：需要配置有效的AI API密钥才能使用AI功能
3. **Ripgrep依赖**：如果系统没有安装ripgrep，会自动回退到glob搜索
4. **测试验证**：迁移后请运行项目测试确保功能正常

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！
