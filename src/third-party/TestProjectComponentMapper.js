class TestProjectComponentMapper {
  constructor() {
    this.componentMappings = this.loadTestProjectMappings();
  }

  /**
   * 加载test-project中实际使用的组件映射
   */
  loadTestProjectMappings() {
    return {
      'vue-count-to': {
        target: 'vue3-count-to',
        patterns: ['vue-count-to', 'CountTo', 'count-to'],
        migrationGuide: `
# vue-count-to 迁移指南

## 安装新版本
\`\`\`bash
npm uninstall vue-count-to
npm install vue3-count-to
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本 (Vue 2)
import CountTo from 'vue-count-to'

// 新版本 (Vue 3)
import CountTo from 'vue3-count-to'
\`\`\`

## 使用方式
组件使用方式基本相同，API兼容。
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:228',
          'test-project/src/views/Components.vue:22-28',
          'test-project/src/components/TestComponent.vue:14'
        ]
      },

      // 基于Components.vue第229行: import Draggable from 'vuedraggable'
      'vuedraggable': {
        target: 'vuedraggable@4.x',
        patterns: ['vuedraggable', 'Draggable', 'draggable'],
        migrationGuide: `
# vuedraggable 迁移指南

## 升级版本
\`\`\`bash
npm install vuedraggable@^4.1.0
\`\`\`

## 主要变更
\`\`\`javascript
// Vue 2 写法
<draggable v-model="list" @start="onStart" @end="onEnd">

// Vue 3 写法 (需要使用v-model:value)
<draggable v-model:value="list" @start="onStart" @end="onEnd">
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:229',
          'test-project/src/views/Components.vue:35-50',
          'test-project/src/components/TestComponent.vue:15',
          'test-project/src/components/TestComponent.vue:5-9'
        ]
      },

      'vue-splitpane': {
        target: 'splitpanes',
        patterns: ['vue-splitpane', 'SplitPane', 'split-pane'],
        migrationGuide: `
# vue-splitpane 迁移指南

## 替换组件
\`\`\`bash
npm uninstall vue-splitpane
npm install splitpanes
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本
import SplitPane from 'vue-splitpane'

// 新版本
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
\`\`\`

## 模板修改
\`\`\`vue
<!-- 旧版本 -->
<split-pane>
  <template slot="paneL">Left</template>
  <template slot="paneR">Right</template>
</split-pane>

<!-- 新版本 -->
<splitpanes>
  <pane>Left</pane>
  <pane>Right</pane>
</splitpanes>
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:230',
          'test-project/src/views/Components.vue:52-70'
        ]
      },

      // 基于Components.vue第231行: import Treeselect from '@riophae/vue-treeselect'
      '@riophae/vue-treeselect': {
        target: 'element-plus tree-select',
        patterns: ['vue-treeselect', 'Treeselect', 'treeselect'],
        migrationGuide: `
# @riophae/vue-treeselect 迁移指南

## 替换为Element Plus
\`\`\`bash
npm uninstall @riophae/vue-treeselect
# Element Plus应该已经安装
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

// 新版本 - 使用Element Plus
// 无需import，直接使用el-tree-select
\`\`\`

## 模板修改
\`\`\`vue
<!-- 旧版本 -->
<treeselect
  v-model="value"
  :options="options"
  :normalizer="normalizer"
/>

<!-- 新版本 -->
<el-tree-select
  v-model="value"
  :data="options"
  :props="{ label: 'label', children: 'children' }"
/>
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:231-232',
          'test-project/src/views/Components.vue:72-90'
        ]
      },

      // 基于Components.vue第234行: import VeLine from 'v-charts/lib/line.common'
      'v-charts': {
        target: 'vue-echarts',
        patterns: ['v-charts', 'VeLine', 've-line'],
        migrationGuide: `
# v-charts 迁移指南

## 替换组件
\`\`\`bash
npm uninstall v-charts
npm install vue-echarts echarts
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本
import VeLine from 'v-charts/lib/line.common'

// 新版本
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([LineChart, GridComponent, CanvasRenderer])
\`\`\`

## 模板修改
\`\`\`vue
<!-- 旧版本 -->
<ve-line :data="chartData" :settings="chartSettings" />

<!-- 新版本 -->
<v-chart :option="echartOptions" />
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:234',
          'test-project/src/views/Components.vue:145'
        ]
      },

      // 基于Components.vue第236行: import VueJsonPretty from 'vue-json-pretty'
      'vue-json-pretty': {
        target: 'vue-json-pretty@^2',
        patterns: ['vue-json-pretty', 'VueJsonPretty'],
        migrationGuide: `
# vue-json-pretty 迁移指南

## 升级版本
\`\`\`bash
npm install vue-json-pretty@^2
\`\`\`

## 代码修改
\`\`\`javascript
// 导入方式相同
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
\`\`\`

## 使用方式
Vue 3版本API基本兼容，无需大幅修改。
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:236-237',
          'test-project/src/views/Components.vue:164-175'
        ]
      },

      // 基于Components.vue第238行: import VueScrollbar from 'vue-scrollbars'
      'vue-scrollbars': {
        target: 'vue3-perfect-scrollbar',
        patterns: ['vue-scrollbars', 'VueScrollbar', 'vue-scrollbar'],
        migrationGuide: `
# vue-scrollbars 迁移指南

## 替换组件
\`\`\`bash
npm uninstall vue-scrollbars
npm install vue3-perfect-scrollbar
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本
import VueScrollbar from 'vue-scrollbars'

// 新版本
import PerfectScrollbar from 'vue3-perfect-scrollbar'
import 'vue3-perfect-scrollbar/dist/vue3-perfect-scrollbar.css'
\`\`\`

## 模板修改
\`\`\`vue
<!-- 旧版本 -->
<vue-scrollbar>
  <div>content</div>
</vue-scrollbar>

<!-- 新版本 -->
<perfect-scrollbar>
  <div>content</div>
</perfect-scrollbar>
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:238',
          'test-project/src/views/Components.vue:196-201'
        ]
      },

      // 基于Components.vue第239行: import { v4 as uuidv4 } from 'vue-uuid'
      'vue-uuid': {
        target: 'vue3-uuid',
        patterns: ['vue-uuid', 'uuidv4', 'uuid'],
        migrationGuide: `
# vue-uuid 迁移指南

## 替换组件
\`\`\`bash
npm uninstall vue-uuid
npm install vue3-uuid
\`\`\`

## 代码修改
\`\`\`javascript
// 旧版本
import { v4 as uuidv4 } from 'vue-uuid'

// 新版本
import UUID from 'vue3-uuid'

// 在main.js中注册
app.use(UUID)

// 在组件中使用
this.$uuid.v4()
\`\`\`
`,
        usageInTestProject: [
          'test-project/src/views/Components.vue:239',
          'test-project/src/views/Components.vue:349-351'
        ]
      }
    };
  }

  /**
   * 获取test-project中实际使用的组件列表
   */
  getUsedComponents() {
    return Object.keys(this.componentMappings);
  }

  /**
   * 获取组件的迁移信息
   */
  getComponentMigration(componentName) {
    return this.componentMappings[componentName] || null;
  }

  /**
   * 获取所有组件的迁移映射
   */
  getAllMappings() {
    return this.componentMappings;
  }

  /**
   * 检查组件是否在test-project中使用
   */
  isUsedInTestProject(componentName) {
    return componentName in this.componentMappings;
  }

  /**
   * 获取组件在test-project中的使用位置
   */
  getUsageLocations(componentName) {
    const mapping = this.componentMappings[componentName];
    return mapping ? mapping.usageInTestProject : [];
  }

  /**
   * 生成test-project迁移摘要
   */
  generateMigrationSummary() {
    const components = this.getUsedComponents();

    return {
      totalComponents: components.length,
      components: components.map(name => ({
        name,
        target: this.componentMappings[name].target,
        usageCount: this.componentMappings[name].usageInTestProject.length
      })),
      migrationComplexity: this.assessMigrationComplexity()
    };
  }

  /**
   * 评估迁移复杂度
   */
  assessMigrationComplexity() {
    const components = this.getUsedComponents();
    let complexity = 'simple';

    // 检查是否有复杂的迁移
    const complexComponents = ['@riophae/vue-treeselect', 'v-charts', 'vue-uuid'];
    const hasComplexMigration = components.some(comp => complexComponents.includes(comp));

    if (hasComplexMigration) {
      complexity = 'complex';
    } else if (components.length > 5) {
      complexity = 'medium';
    }

    return complexity;
  }
}

module.exports = TestProjectComponentMapper;
