const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');
const glob = require('glob');

/**
 * ComponentSearcher - 组件搜索器
 *
 * 功能：
 * - 使用ripgrep或glob进行高效文件搜索
 * - 识别项目中使用的第三方组件
 * - 分析组件使用模式和位置
 * - 提供搜索结果的结构化数据
 */
class ComponentSearcher {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      useRipgrep: true,
      verbose: false,
      includePatterns: ['**/*.vue', '**/*.js', '**/*.ts', '**/*.jsx', '**/*.tsx'],
      excludePatterns: ['node_modules/**', 'dist/**', 'build/**', '**/*.test.*', '**/*.spec.*'],
      ...options
    };

    // 需要迁移的第三方组件映射
    this.componentMappings = this.loadComponentMappings();
  }

  /**
   * 加载组件映射配置
   */
  loadComponentMappings() {
    return {
      'vue-count-to': {
        target: 'vue3-count-to',
        patterns: ['vue-count-to', 'countTo', 'count-to'],
        migrationGuide: 'import CountTo from "vue3-count-to"'
      },
      'vue-splitpane': {
        target: 'splitpanes',
        patterns: ['vue-splitpane', 'splitpane'],
        migrationGuide: `import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

<splitpanes class="default-theme">
  <pane v-for="i in 3" :key="i">
    <div>{{ i }}</div>
  </pane>
</splitpanes>
`
      },
      '@riophae/vue-treeselect': {
        target: 'element-plus tree-select',
        patterns: ['vue-treeselect', 'treeselect'],
        migrationGuide: 'Use Element Plus el-tree-select component'
      },
      'v-charts': {
        target: 'vue-echarts',
        patterns: ['v-charts', 'VChart'],
        migrationGuide: 'import VChart from "vue-echarts"'
      },
      'vue-scrollbars': {
        target: 'vue3-perfect-scrollbar',
        patterns: ['vue-scrollbars', 'scrollbars'],
        migrationGuide: 'import PerfectScrollbar from "vue3-perfect-scrollbar"'
      },
      'vue-uuid': {
        target: 'vue3-uuid',
        patterns: ['vue-uuid', 'UUID'],
        migrationGuide: 'import UUID from "vue3-uuid"'
      },
      '@tinymce/tinymce-vue': {
        target: '@tinymce/tinymce-vue@^4',
        patterns: ['tinymce-vue', 'Editor'],
        migrationGuide: 'Update to Vue 3 compatible version'
      },
      '@wangeditor/editor-for-vue': {
        target: '@wangeditor/editor-for-vue@next',
        patterns: ['wangeditor', 'editor-for-vue'],
        migrationGuide: 'Update to Vue 3 compatible version'
      },
      'vue-text-format': {
        target: 'vue-text-format@^2',
        patterns: ['vue-text-format', 'format'],
        migrationGuide: 'Update to Vue 3 compatible version'
      },
      'vuepdf': {
        target: 'vue3-pdfjs',
        patterns: ['vuepdf', 'pdf'],
        migrationGuide: 'import VuePdf from "vue3-pdfjs"'
      },
      'vue-json-pretty': {
        target: 'vue-json-pretty@^2',
        patterns: ['vue-json-pretty', 'json-pretty'],
        migrationGuide: 'Update to Vue 3 compatible version'
      },
      'vue2-tree-org': {
        target: 'vue3-tree-org',
        patterns: ['vue2-tree-org', 'tree-org'],
        migrationGuide: 'import TreeOrg from "vue3-tree-org"'
      },
      'vue-template-compiler': {
        target: '@vue/compiler-sfc',
        patterns: ['vue-template-compiler'],
        migrationGuide: 'Replace with @vue/compiler-sfc'
      }
    };
  }

  /**
   * 搜索所有第三方组件的使用情况
   */
  async searchAllComponents() {
    console.log(chalk.gray('🔍 搜索第三方组件使用情况...'));

    const results = {
      components: [],
      totalFiles: 0,
      searchMethod: this.options.useRipgrep ? 'ripgrep' : 'glob'
    };

    // 首先检查package.json中的依赖
    const packageDeps = await this.getPackageDependencies();
    const relevantDeps = Object.keys(this.componentMappings).filter(dep =>
      packageDeps.includes(dep)
    );

    if (relevantDeps.length === 0) {
      console.log(chalk.gray('未在package.json中发现需要迁移的第三方组件'));
      return results;
    }

    console.log(chalk.gray(`在package.json中发现 ${relevantDeps.length} 个需要迁移的组件`));

    // 为每个相关依赖搜索使用情况
    for (const depName of relevantDeps) {
      const componentResult = await this.searchComponent(depName);
      if (componentResult.files.length > 0) {
        results.components.push(componentResult);
        results.totalFiles += componentResult.files.length;
      }
    }

    return results;
  }

  /**
   * 获取package.json中的依赖列表
   */
  async getPackageDependencies() {
    try {
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        return [];
      }

      const packageJson = await fs.readJson(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      return Object.keys(allDeps);
    } catch (error) {
      console.warn(chalk.yellow(`读取package.json失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 搜索单个组件的使用情况
   */
  async searchComponent(componentName) {
    const mapping = this.componentMappings[componentName];
    const result = {
      name: componentName,
      target: mapping.target,
      migrationGuide: mapping.migrationGuide,
      files: []
    };

    if (this.options.verbose) {
      console.log(chalk.gray(`  搜索组件: ${componentName}`));
    }

    // 使用ripgrep或glob搜索
    const searchResults = this.options.useRipgrep
      ? await this.searchWithRipgrep(mapping.patterns)
      : await this.searchWithGlob(mapping.patterns);

    // 处理搜索结果
    for (const fileResult of searchResults) {
      const fileInfo = await this.analyzeFileUsage(fileResult.path, mapping.patterns);
      if (fileInfo.hasUsage) {
        result.files.push({
          path: fileResult.path,
          usages: fileInfo.usages,
          complexity: fileInfo.complexity
        });
      }
    }

    if (this.options.verbose) {
      console.log(chalk.gray(`    找到 ${result.files.length} 个使用文件`));
    }

    return result;
  }

  /**
   * 使用ripgrep搜索
   */
  async searchWithRipgrep(patterns) {
    try {
      const results = [];

      for (const pattern of patterns) {
        const rgResults = await this.executeRipgrep(pattern);
        results.push(...rgResults);
      }

      // 去重
      const uniqueFiles = [...new Set(results.map(r => r.path))];
      return uniqueFiles.map(path => ({ path }));
    } catch (error) {
      console.warn(chalk.yellow(`ripgrep搜索失败，回退到glob: ${error.message}`));
      return await this.searchWithGlob(patterns);
    }
  }

  /**
   * 执行ripgrep命令
   */
  async executeRipgrep(pattern) {
    return new Promise((resolve, reject) => {
      const args = [
        '--json',
        '--type', 'js',
        '--type', 'ts',
        '--type', 'vue',
        pattern,
        this.projectPath
      ];

      const rg = spawn('rg', args);
      let stdout = '';
      let stderr = '';

      rg.stdout.on('data', data => {
        stdout += data.toString();
      });

      rg.stderr.on('data', data => {
        stderr += data.toString();
      });

      rg.on('close', code => {
        if (code === 0 || code === 1) { // 1 means no matches found
          try {
            const results = [];
            const lines = stdout.trim().split('\n').filter(line => line);

            for (const line of lines) {
              const data = JSON.parse(line);
              if (data.type === 'match') {
                results.push({
                  path: data.data.path.text,
                  line: data.data.line_number,
                  content: data.data.lines.text
                });
              }
            }

            resolve(results);
          } catch (error) {
            reject(new Error(`解析ripgrep输出失败: ${error.message}`));
          }
        } else {
          reject(new Error(`ripgrep执行失败: ${stderr}`));
        }
      });

      rg.on('error', err => {
        reject(new Error(`ripgrep命令执行失败: ${err.message}`));
      });
    });
  }

  /**
   * 使用glob搜索
   */
  async searchWithGlob(patterns) {
    const results = [];

    for (const includePattern of this.options.includePatterns) {
      const files = glob.sync(includePattern, {
        cwd: this.projectPath,
        ignore: this.options.excludePatterns,
        absolute: true
      });

      for (const filePath of files) {
        try {
          const content = await fs.readFile(filePath, 'utf8');
          const hasPattern = patterns.some(pattern =>
            content.includes(pattern)
          );

          if (hasPattern) {
            results.push({ path: filePath });
          }
        } catch (error) {
          // 忽略读取失败的文件
        }
      }
    }

    return results;
  }

  /**
   * 分析文件中的组件使用情况
   */
  async analyzeFileUsage(filePath, patterns) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const lines = content.split('\n');
      const usages = [];

      // 查找所有使用位置
      lines.forEach((line, index) => {
        patterns.forEach(pattern => {
          if (line.includes(pattern)) {
            usages.push({
              line: index + 1,
              content: line.trim(),
              pattern: pattern,
              type: this.detectUsageType(line)
            });
          }
        });
      });

      return {
        hasUsage: usages.length > 0,
        usages: usages,
        complexity: this.calculateComplexity(content, usages)
      };
    } catch (error) {
      return {
        hasUsage: false,
        usages: [],
        complexity: 'unknown'
      };
    }
  }

  /**
   * 检测使用类型
   */
  detectUsageType(line) {
    if (line.includes('import')) return 'import';
    if (line.includes('require')) return 'require';
    if (line.includes('components:')) return 'registration';
    if (line.includes('<')) return 'template';
    return 'usage';
  }

  /**
   * 计算文件复杂度
   */
  calculateComplexity(content, usages) {
    const lines = content.split('\n').length;
    const usageCount = usages.length;

    if (lines < 50 && usageCount < 3) return 'simple';
    if (lines < 200 && usageCount < 10) return 'medium';
    return 'complex';
  }
}

module.exports = ComponentSearcher;
