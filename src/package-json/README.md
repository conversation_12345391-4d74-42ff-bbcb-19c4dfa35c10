# Package.json 处理模块

本模块负责 Vue 2 到 Vue 3 迁移过程中的所有 package.json 相关操作，包括依赖合并、版本升级和第三方组件映射。

## 架构概述

重构后的 package.json 处理模块采用了模块化设计，将原来 AutoMigrator 中的复杂逻辑拆分为多个专门的类：

```
src/package-json/
├── PackageJsonManager.js      # 主管理器，统一协调所有操作
├── PackageJsonMerger.js       # 负责源项目和目标项目的 package.json 合并
├── PackageUpgrader.js         # 负责依赖版本升级
├── PackageDependencyChecker.js # 负责依赖兼容性检查
└── README.md                  # 本文档
```

## 核心组件

### 1. PackageJsonManager

**职责**：统一管理所有 package.json 相关操作的主入口

**主要功能**：
- 协调源到目标模式和单项目模式的处理流程
- 集成 PackageJsonMerger、PackageUpgrader 和第三方组件映射
- 生成详细的迁移报告
- 提供 package.json 验证功能

**使用示例**：
```javascript
const PackageJsonManager = require('./src/package-json/PackageJsonManager');

// 源到目标模式
const manager = new PackageJsonManager({
  sourceProjectPath: '/path/to/vue2-project',
  targetProjectPath: '/path/to/vue3-project',
  sourceToTargetMode: true,
  migrationMode: true,
  preserveVue3Dependencies: true,
  enableThirdPartyMapping: true,
  verbose: true,
  dryRun: false
});

const result = await manager.processPackageJson();
manager.printMigrationReport(result);
```

### 2. PackageJsonMerger

**职责**：智能合并源项目和目标项目的 package.json

**核心特性**：
- **智能依赖合并**：结合目标项目的现有依赖和源项目的需求依赖
- **Vue 2 官方依赖过滤**：自动跳过不兼容的 Vue 2 官方依赖
- **第三方组件映射**：集成 ComponentDependencyMapper 进行组件升级
- **配置灵活性**：支持多种合并策略

**合并策略**：
1. **基本信息**：保留目标项目信息，补充缺失字段
2. **脚本**：保留目标项目重要脚本，添加缺失脚本
3. **依赖**：智能合并，优先保留目标项目的 Vue 3 依赖
4. **其他字段**：合并 browserslist、engines 等配置

### 3. 集成第三方组件映射

重构后的模块完全集成了 ComponentDependencyMapper 的功能：

**映射流程**：
1. 检查源项目中的第三方组件依赖
2. 查找对应的 Vue 3 兼容版本
3. 在合并过程中自动替换依赖
4. 生成详细的映射报告

**支持的映射类型**：
- `vue-count-to` → `vue3-count-to`
- `vue-uuid` → `vue3-uuid`
- `element-ui` → `element-plus`
- 更多映射请参考 `src/third-party/docs/` 目录

## 使用场景

### 场景 1：源到目标模式

当你有一个 Vue 2 源项目和一个 Vue 3 目标项目模板时：

```javascript
const manager = new PackageJsonManager({
  sourceProjectPath: './vue2-project',
  targetProjectPath: './vue3-project',
  sourceToTargetMode: true,
  preserveVue3Dependencies: true,
  enableThirdPartyMapping: true
});

const result = await manager.processPackageJson();
```

**处理流程**：
1. 合并源项目和目标项目的 package.json
2. 升级基础 Vue 3 依赖
3. 处理第三方组件映射
4. 生成迁移报告

### 场景 2：单项目模式

当你直接在现有项目中进行迁移时：

```javascript
const manager = new PackageJsonManager({
  workingPath: './my-project',
  sourceToTargetMode: false,
  migrationMode: true,
  enableThirdPartyMapping: true
});

const result = await manager.processPackageJson();
```

**处理流程**：
1. 升级项目中的 Vue 相关依赖
2. 处理第三方组件映射
3. 生成迁移报告

## 配置选项

### PackageJsonManager 选项

| 选项                         | 类型      | 默认值   | 说明                 |
|----------------------------|---------|-------|--------------------|
| `sourceProjectPath`        | string  | null  | 源项目路径（源到目标模式）      |
| `targetProjectPath`        | string  | null  | 目标项目路径（源到目标模式）     |
| `workingPath`              | string  | null  | 工作目录路径（单项目模式）      |
| `sourceToTargetMode`       | boolean | false | 是否启用源到目标模式         |
| `migrationMode`            | boolean | false | 是否为迁移模式            |
| `preserveVue3Dependencies` | boolean | true  | 是否保留目标项目的 Vue 3 依赖 |
| `enableThirdPartyMapping`  | boolean | true  | 是否启用第三方组件映射        |
| `verbose`                  | boolean | false | 是否显示详细日志           |
| `dryRun`                   | boolean | false | 是否为试运行模式           |

### PackageJsonMerger 选项

| 选项                           | 类型      | 默认值   | 说明            |
|------------------------------|---------|-------|---------------|
| `preserveTargetDependencies` | boolean | true  | 是否保留目标项目的现有依赖 |
| `enableThirdPartyMapping`    | boolean | true  | 是否启用第三方组件映射   |
| `verbose`                    | boolean | false | 是否显示详细日志      |
| `dryRun`                     | boolean | false | 是否为试运行模式      |

## 迁移报告

处理完成后，系统会生成详细的迁移报告：

```javascript
{
  summary: {
    totalChanges: 15,
    mergeChanges: 8,
    upgradeChanges: 5,
    mappingChanges: 2
  },
  details: {
    merge: [
      '添加 dependencies: axios@^0.27.2',
      '添加 dependencies: lodash@^4.17.21'
    ],
    upgrade: {
      upgraded: [
        { name: 'vue', oldVersion: '^2.6.14', newVersion: '^3.4.0' }
      ],
      added: [
        { name: '@vue/compiler-sfc', version: '^3.4.0' }
      ]
    },
    mapping: [
      { source: 'vue-count-to', target: 'vue3-count-to' }
    ]
  }
}
```

## 错误处理

模块提供了完善的错误处理机制：

1. **文件不存在**：自动检测并报告缺失的 package.json 文件
2. **JSON 格式错误**：优雅处理无效的 JSON 文件
3. **依赖冲突**：智能解决版本冲突
4. **网络错误**：处理依赖映射加载失败的情况

## 测试

模块包含完整的测试套件：

```bash
# 运行所有 package.json 相关测试
npm test -- test/package-json-*.test.js

# 运行特定测试
npm test -- test/package-json-merger.test.js
npm test -- test/package-json-manager.test.js
npm test -- test/package-json-integration.test.js
```

## 扩展性

### 添加新的依赖映射

1. 在 `config/package-recommend.json` 中添加映射规则
2. 在 `src/third-party/docs/` 中添加迁移文档
3. 更新 ComponentDependencyMapper 的配置

### 自定义合并策略

可以通过继承 PackageJsonMerger 类来实现自定义的合并策略：

```javascript
class CustomPackageJsonMerger extends PackageJsonMerger {
  shouldAddDependency(depName, depVersion) {
    // 自定义依赖添加逻辑
    return super.shouldAddDependency(depName, depVersion);
  }
}
```

## 与 AutoMigrator 的集成

重构后的模块已经完全集成到 AutoMigrator 中：

```javascript
// AutoMigrator.js 中的使用
const packageJsonManager = new PackageJsonManager({
  sourceProjectPath: this.sourceProjectPath,
  targetProjectPath: this.targetProjectPath,
  workingPath: this.workingPath,
  sourceToTargetMode: this.sourceToTargetMode,
  migrationMode: true,
  preserveVue3Dependencies: true,
  enableThirdPartyMapping: true,
  verbose: this.finalOptions.verbose,
  dryRun: this.finalOptions.dryRun
});

const result = await packageJsonManager.processPackageJson();
```

这样的设计使得 package.json 处理逻辑更加清晰、可维护和可测试。
