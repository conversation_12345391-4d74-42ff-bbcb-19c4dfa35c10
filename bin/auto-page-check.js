#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const RuntimePageChecker = require('../src/runtime-validation/RuntimePageChecker');

/**
 * 简化的页面验证工具 - 一键自动修复
 * 
 * 使用方法:
 * node bin/auto-page-check.js [project-path]
 */

async function main() {
  const projectPath = process.argv[2] || process.cwd();
  
  console.log(chalk.blue('🚀 Vue 页面自动验证与修复工具'));
  console.log(chalk.gray(`   项目路径: ${path.resolve(projectPath)}`));
  console.log(chalk.gray(`   模式: 自动修复模式\n`));

  // 验证项目路径
  if (!await fs.pathExists(projectPath)) {
    console.error(chalk.red(`❌ 项目路径不存在: ${projectPath}`));
    process.exit(1);
  }

  // 检查是否是 Vue 项目
  const packageJsonPath = path.join(projectPath, 'package.json');
  if (!await fs.pathExists(packageJsonPath)) {
    console.error(chalk.red('❌ 未找到 package.json，请确认这是一个有效的项目目录'));
    process.exit(1);
  }

  try {
    // 创建页面检查器，启用自动修复
    const checker = new RuntimePageChecker(projectPath, {
      // 自动修复配置
      autoFix: true,
      maxFixAttempts: 3,
      
      // 服务器配置
      port: 3000,
      timeout: 30000,
      pageTimeout: 10000,
      waitForServer: 60000,
      
      // 浏览器配置
      headless: true,
      
      // AI 配置
      useAI: true,
      
      // 输出配置
      verbose: true,
      outputDir: path.join(projectPath, 'validation-reports')
    });

    console.log(chalk.blue('🔧 配置信息:'));
    console.log(chalk.gray('   - 自动修复: 启用'));
    console.log(chalk.gray('   - 最大修复尝试: 3 次'));
    console.log(chalk.gray('   - AI 辅助: 启用'));
    console.log(chalk.gray('   - 详细输出: 启用'));
    console.log(chalk.gray('   - 无头浏览器: 启用\n'));

    // 执行检查
    const result = await checker.checkAllPages();

    if (result.success) {
      console.log(chalk.green('\n🎉 页面验证完成！'));
      
      const validation = result.results.validation;
      if (validation?.report) {
        const successRate = parseFloat(validation.report.summary.successRate);
        const { successful, failed, total } = validation.report.summary;
        
        console.log(chalk.blue('\n📊 最终结果:'));
        console.log(chalk.gray(`   总页面数: ${total}`));
        console.log(chalk.green(`   成功: ${successful}`));
        console.log(chalk.red(`   失败: ${failed}`));
        console.log(chalk.blue(`   成功率: ${successRate}%`));
        
        if (successRate === 100) {
          console.log(chalk.green('\n✨ 所有页面都运行正常！'));
        } else if (successRate >= 80) {
          console.log(chalk.yellow(`\n⚠️  还有 ${failed} 个页面需要手动修复`));
          console.log(chalk.gray(`   详细报告: ${checker.options.outputDir}`));
        } else {
          console.log(chalk.red(`\n🚨 发现较多问题，建议检查详细报告`));
          console.log(chalk.gray(`   报告位置: ${checker.options.outputDir}`));
        }
      }
      
      console.log(chalk.blue(`\n⏱️  总耗时: ${result.duration}ms`));
      
    } else {
      console.error(chalk.red('\n❌ 页面验证失败'));
      console.error(chalk.red(`   错误: ${result.error}`));
      
      if (result.results.errors.length > 0) {
        console.error(chalk.red('\n详细错误:'));
        for (const error of result.results.errors) {
          console.error(chalk.red(`   - ${error}`));
        }
      }
      
      process.exit(1);
    }

  } catch (error) {
    console.error(chalk.red(`\n❌ 执行失败: ${error.message}`));
    if (error.stack) {
      console.error(chalk.gray(error.stack));
    }
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red(`❌ 未捕获的异常: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red(`❌ 未处理的 Promise 拒绝: ${reason}`));
  process.exit(1);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到中断信号，正在清理资源...'));
  process.exit(0);
});

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
${chalk.blue('Vue 页面自动验证与修复工具')}

${chalk.yellow('使用方法:')}
  node bin/auto-page-check.js [project-path]

${chalk.yellow('参数:')}
  project-path    项目路径 (默认: 当前目录)

${chalk.yellow('功能:')}
  - 自动解析 Vue Router 路由
  - 启动开发服务器
  - 验证所有页面运行状态
  - 自动修复发现的错误
  - 生成详细报告

${chalk.yellow('示例:')}
  node bin/auto-page-check.js
  node bin/auto-page-check.js /path/to/vue-project
  node bin/auto-page-check.js --help
`);
  process.exit(0);
}

// 运行主程序
main().catch(error => {
  console.error(chalk.red(`❌ 程序异常退出: ${error.message}`));
  process.exit(1);
});
