#!/usr/bin/env node

const path = require('path')
const chalk = require('chalk')
const { Command } = require('commander')
const ThirdPartyMigrationAgent = require('../src/third-party/ThirdPartyMigrationAgent')
const ComponentSearcher = require('../src/third-party/ComponentSearcher')

const program = new Command()

program
	.name('third-party-migrator')
	.description('Vue 2 到 Vue 3 第三方组件自动化迁移工具')
	.version('1.0.0')

program
	.command('migrate')
	.description('执行第三方组件迁移')
	.argument('<project-path>', '项目路径')
	.option('--dry-run', '预览模式，不实际修改文件')
	.option('--verbose', '详细输出')
	.option('--max-file-size <size>', '小文件直接AI翻译的最大行数', '200')
	.option('--max-attempts <attempts>', '最大尝试次数', '3')
	.option('--no-ripgrep', '禁用ripgrep，使用glob搜索')
	.option('--ai-api-key <key>', 'AI API密钥')
	.action(async (projectPath, options) => {
		try {
			console.log(chalk.blue('🚀 Vue 2 到 Vue 3 第三方组件迁移工具'))
			console.log(chalk.gray(`项目路径: ${path.resolve(projectPath)}`))

			if (options.dryRun) {
				console.log(chalk.yellow('📝 预览模式：将显示需要修改的内容，但不会实际修改文件'))
			}

			const agent = new ThirdPartyMigrationAgent(projectPath, {
				maxFileSize: parseInt(options.maxFileSize),
				maxAttempts: parseInt(options.maxAttempts),
				dryRun: options.dryRun,
				verbose: options.verbose,
				useRipgrep: options.ripgrep !== false,
				apiKey: options.aiApiKey || process.env.AI_API_KEY
			})

			const result = await agent.migrate()

			if (result.success) {
				console.log(chalk.green('\n✅ 迁移完成！'))
				console.log(chalk.gray(`处理文件: ${result.stats.processedFiles}/${result.stats.totalFiles}`))
				console.log(chalk.gray(`AI迁移: ${result.stats.aiMigratedFiles}`))
				console.log(chalk.gray(`规则迁移: ${result.stats.ruleMigratedFiles}`))
			} else {
				console.log(chalk.red('\n❌ 迁移失败'))
				console.log(chalk.red(result.message))
				process.exit(1)
			}
		} catch (error) {
			console.error(chalk.red('❌ 执行失败:'), error.message)
			if (options.verbose) {
				console.error(error.stack)
			}
			process.exit(1)
		}
	})

program
	.command('analyze')
	.description('分析项目中的第三方组件使用情况')
	.argument('<project-path>', '项目路径')
	.option('--verbose', '详细输出')
	.option('--no-ripgrep', '禁用ripgrep，使用glob搜索')
	.option('--output <file>', '输出分析结果到文件')
	.action(async (projectPath, options) => {
		try {
			console.log(chalk.blue('🔍 分析第三方组件使用情况'))

			const searcher = new ComponentSearcher(projectPath, {
				useRipgrep: options.ripgrep !== false,
				verbose: options.verbose
			})

			const result = await searcher.searchAllComponents()

			console.log(chalk.blue('\n📊 分析结果:'))
			console.log(`发现组件: ${result.components.length}`)
			console.log(`涉及文件: ${result.totalFiles}`)
			console.log(`搜索方法: ${result.searchMethod}`)

			if (result.components.length > 0) {
				console.log(chalk.blue('\n需要迁移的组件:'))
				result.components.forEach(component => {
					console.log(`\n📦 ${component.name}`)
					console.log(`  目标: ${component.target}`)
					console.log(`  文件数: ${component.files.length}`)

					if (options.verbose) {
						component.files.forEach(file => {
							const relativePath = path.relative(projectPath, file.path)
							console.log(`    - ${relativePath} (${file.complexity})`)
						})
					}
				})
			}

			// 输出到文件
			if (options.output) {
				const fs = require('fs-extra')
				await fs.writeJson(options.output, result, { spaces: 2 })
				console.log(chalk.green(`\n分析结果已保存到: ${options.output}`))
			}

		} catch (error) {
			console.error(chalk.red('❌ 分析失败:'), error.message)
			if (options.verbose) {
				console.error(error.stack)
			}
			process.exit(1)
		}
	})

program.on('command:*', () => {
	console.error(chalk.red('❌ 未知命令:'), program.args.join(' '))
	console.log(chalk.gray('使用 --help 查看可用命令'))
	process.exit(1)
})

// 解析命令行参数
program.parse()

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
	program.outputHelp()
}
