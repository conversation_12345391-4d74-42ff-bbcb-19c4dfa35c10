#!/usr/bin/env node

/**
 * Vue 页面运行时验证工具演示
 * 
 * 这个示例展示如何使用页面验证工具来检查 Vue 项目中的所有页面
 */

const path = require('path');
const chalk = require('chalk');
const RuntimePageChecker = require('../src/runtime-validation/RuntimePageChecker');
const RouteParser = require('../src/runtime-validation/RouteParser');

async function runDemo() {
  console.log(chalk.blue('🚀 Vue 页面运行时验证工具演示\n'));

  // 演示项目路径
  const testProjects = [
    {
      name: 'Vue 3 测试项目',
      path: path.join(__dirname, '../test-project'),
      description: '包含 Vue 3 + Element Plus 的测试项目'
    },
    {
      name: 'Vue 2 测试项目', 
      path: path.join(__dirname, '../test-build-fixer'),
      description: '简单的 Vue 2 项目'
    }
  ];

  for (const project of testProjects) {
    console.log(chalk.yellow(`\n📁 ${project.name}`));
    console.log(chalk.gray(`   ${project.description}`));
    console.log(chalk.gray(`   路径: ${project.path}`));

    try {
      // 1. 演示路由解析
      console.log(chalk.blue('\n🔍 步骤 1: 解析项目路由'));
      
      const parser = new RouteParser(project.path, {
        verbose: false,
        useAI: false // 演示中禁用 AI
      });

      const parseResult = await parser.parseRoutes();
      
      if (parseResult.success) {
        const routes = parser.getRoutePaths();
        console.log(chalk.green(`   ✅ 成功解析 ${routes.length} 个路由:`));
        
        routes.forEach(route => {
          console.log(chalk.gray(`      - ${route.path}${route.name ? ` (${route.name})` : ''}`));
        });

        // 2. 演示页面验证配置（不实际运行）
        console.log(chalk.blue('\n🔧 步骤 2: 页面验证配置'));
        console.log(chalk.gray('   配置选项:'));
        console.log(chalk.gray('   - 端口: 3000'));
        console.log(chalk.gray('   - 无头模式: true'));
        console.log(chalk.gray('   - 页面超时: 5000ms'));
        console.log(chalk.gray('   - 自动修复: false'));

        // 3. 模拟验证结果
        console.log(chalk.blue('\n📊 步骤 3: 模拟验证结果'));
        
        const mockResults = generateMockValidationResults(routes);
        displayMockResults(mockResults);

      } else {
        console.log(chalk.red(`   ❌ 路由解析失败:`));
        parseResult.errors.forEach(error => {
          console.log(chalk.red(`      - ${error}`));
        });
      }

    } catch (error) {
      console.log(chalk.red(`   ❌ 演示失败: ${error.message}`));
    }

    console.log(chalk.gray('\n' + '─'.repeat(60)));
  }

  // 显示使用说明
  showUsageInstructions();
}

/**
 * 生成模拟的验证结果
 */
function generateMockValidationResults(routes) {
  const results = routes.map((route, index) => {
    // 模拟一些页面有错误
    const hasError = Math.random() < 0.2; // 20% 的页面有错误
    const loadTime = Math.floor(Math.random() * 2000) + 500; // 500-2500ms

    return {
      route: route,
      url: `http://localhost:3000${route.path}`,
      success: !hasError,
      errors: hasError ? [
        `Console Error: ${generateMockError()}`,
        ...(Math.random() < 0.5 ? [`Network Error: Failed to load resource`] : [])
      ] : [],
      warnings: Math.random() < 0.3 ? [`Console Warning: ${generateMockWarning()}`] : [],
      loadTime: loadTime,
      timestamp: new Date().toISOString()
    };
  });

  return {
    summary: {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      successRate: ((results.filter(r => r.success).length / results.length) * 100).toFixed(2)
    },
    results: results,
    failedPages: results.filter(r => !r.success)
  };
}

/**
 * 生成模拟错误信息
 */
function generateMockError() {
  const errors = [
    'Cannot read property \'data\' of undefined',
    'ReferenceError: Vue is not defined',
    'TypeError: this.$router.push is not a function',
    'Error: Component failed to mount',
    'Uncaught TypeError: Cannot read property \'$emit\' of undefined'
  ];
  return errors[Math.floor(Math.random() * errors.length)];
}

/**
 * 生成模拟警告信息
 */
function generateMockWarning() {
  const warnings = [
    'Vue component prop validation failed',
    'Deprecated API usage detected',
    'Performance warning: Large component tree',
    'Missing key prop in list rendering'
  ];
  return warnings[Math.floor(Math.random() * warnings.length)];
}

/**
 * 显示模拟验证结果
 */
function displayMockResults(mockResults) {
  console.log(chalk.gray('   模拟验证结果:'));
  console.log(chalk.green(`   ✅ 成功: ${mockResults.summary.successful}`));
  console.log(chalk.red(`   ❌ 失败: ${mockResults.summary.failed}`));
  console.log(chalk.blue(`   📊 成功率: ${mockResults.summary.successRate}%`));

  if (mockResults.failedPages.length > 0) {
    console.log(chalk.red('\n   失败的页面:'));
    mockResults.failedPages.slice(0, 3).forEach(page => { // 只显示前3个
      console.log(chalk.red(`      - ${page.route.path}: ${page.errors[0]}`));
    });
    
    if (mockResults.failedPages.length > 3) {
      console.log(chalk.gray(`      ... 还有 ${mockResults.failedPages.length - 3} 个失败页面`));
    }
  }

  // 显示性能统计
  const avgLoadTime = mockResults.results.reduce((sum, r) => sum + r.loadTime, 0) / mockResults.results.length;
  console.log(chalk.blue(`\n   📈 平均加载时间: ${avgLoadTime.toFixed(0)}ms`));
}

/**
 * 显示使用说明
 */
function showUsageInstructions() {
  console.log(chalk.blue('\n📖 实际使用说明:\n'));

  console.log(chalk.yellow('1. 仅解析路由:'));
  console.log(chalk.gray('   node bin/page-validator.js parse-routes [project-path]'));

  console.log(chalk.yellow('\n2. 完整页面验证:'));
  console.log(chalk.gray('   node bin/page-validator.js check [project-path]'));

  console.log(chalk.yellow('\n3. 启用自动修复:'));
  console.log(chalk.gray('   node bin/page-validator.js check --auto-fix --verbose'));

  console.log(chalk.yellow('\n4. 使用外部服务器:'));
  console.log(chalk.gray('   node bin/page-validator.js check --base-url http://localhost:8080'));

  console.log(chalk.yellow('\n5. 验证单个 URL:'));
  console.log(chalk.gray('   node bin/page-validator.js validate-url http://localhost:3000/about'));

  console.log(chalk.blue('\n💡 提示:'));
  console.log(chalk.gray('   - 使用 --verbose 获取详细输出'));
  console.log(chalk.gray('   - 使用 --no-headless 显示浏览器界面（调试模式）'));
  console.log(chalk.gray('   - 验证报告会自动保存到 validation-reports/ 目录'));
  console.log(chalk.gray('   - 支持 Vue 2 和 Vue 3 项目'));

  console.log(chalk.green('\n🎉 演示完成！'));
}

// 运行演示
if (require.main === module) {
  runDemo().catch(error => {
    console.error(chalk.red(`❌ 演示失败: ${error.message}`));
    process.exit(1);
  });
}

module.exports = { runDemo };
