#!/usr/bin/env node

/**
 * AutoLoginManager 使用示例
 * 
 * 这个示例展示了如何使用 AutoLoginManager 进行自动登录
 */

const AutoLoginManager = require('../src/runtime-validation/AutoLoginManager');
const puppeteer = require('puppeteer');
const path = require('path');

async function demonstrateAutoLogin() {
  console.log('🚀 AutoLoginManager 使用示例');
  console.log('================================\n');

  let browser;
  let page;

  try {
    // 1. 创建 AutoLoginManager 实例
    console.log('📝 步骤 1: 创建 AutoLoginManager 实例');
    const loginManager = new AutoLoginManager({
      username: 'demo_user',
      password: 'demo_password',
      verbose: true,
      aiEnabled: true,
      configPath: path.join(__dirname, '.demo-login-config.json'),
      maxRetries: 3
    });
    console.log('✅ AutoLoginManager 创建成功\n');

    // 2. 启动浏览器
    console.log('📝 步骤 2: 启动浏览器');
    browser = await puppeteer.launch({
      headless: false, // 显示浏览器以便观察
      defaultViewport: { width: 1280, height: 720 }
    });
    page = await browser.newPage();
    console.log('✅ 浏览器启动成功\n');

    // 3. 模拟访问需要登录的页面
    console.log('📝 步骤 3: 访问登录页面');
    
    // 这里我们创建一个简单的 HTML 登录页面用于演示
    const loginPageHTML = `
<!DOCTYPE html>
<html>
<head>
    <title>Demo Login Page</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 50px; }
        .login-form { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; }
        input { width: 100%; padding: 10px; margin: 10px 0; box-sizing: border-box; }
        .el-button--primary { background: #409EFF; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .el-button--primary:hover { background: #66b1ff; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>用户登录</h2>
        <form id="loginForm">
            <input type="text" name="username" placeholder="用户名" required>
            <input type="password" name="password" placeholder="密码" required>
            <button type="submit" class="el-button--primary">登录</button>
        </form>
    </div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const username = document.querySelector('input[name="username"]').value;
            const password = document.querySelector('input[name="password"]').value;
            
            // 模拟登录验证
            if (username === 'demo_user' && password === 'demo_password') {
                alert('登录成功！');
                // 模拟跳转到主页
                document.body.innerHTML = '<h1>欢迎来到主页！</h1><p>登录成功，当前用户: ' + username + '</p>';
                // 改变 URL 以模拟页面跳转
                window.history.pushState({}, '', '/dashboard');
            } else {
                alert('用户名或密码错误！');
            }
        });
    </script>
</body>
</html>`;

    // 设置页面内容
    await page.setContent(loginPageHTML);
    await page.evaluate(() => {
      // 模拟登录页面的 URL
      window.history.pushState({}, '', '/login');
    });
    
    console.log('✅ 登录页面加载完成\n');

    // 4. 使用 AutoLoginManager 进行自动登录
    console.log('📝 步骤 4: 执行自动登录');
    console.log('🔑 开始自动登录流程...\n');
    
    const loginSuccess = await loginManager.attemptLogin(page);
    
    if (loginSuccess) {
      console.log('\n🎉 自动登录成功！');
      
      // 等待一下让用户看到结果
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 检查当前页面状态
      const currentUrl = await page.evaluate(() => window.location.pathname);
      const pageContent = await page.evaluate(() => document.body.textContent);
      
      console.log(`📍 当前页面: ${currentUrl}`);
      console.log(`📄 页面内容预览: ${pageContent.substring(0, 100)}...`);
      
    } else {
      console.log('\n❌ 自动登录失败');
      console.log('可能的原因:');
      console.log('- 用户名或密码不正确');
      console.log('- 页面结构与预期不符');
      console.log('- 网络连接问题');
    }

    // 5. 展示配置文件内容（如果存在）
    console.log('\n📝 步骤 5: 检查保存的登录配置');
    const configLoaded = await loginManager.loadLoginConfig();
    if (configLoaded && loginManager.loginConfig) {
      console.log('📄 保存的登录配置:');
      console.log(JSON.stringify(loginManager.loginConfig, null, 2));
    } else {
      console.log('ℹ️  暂无保存的登录配置');
    }

  } catch (error) {
    console.error('\n❌ 示例执行失败:', error.message);
    console.error(error.stack);
  } finally {
    // 清理资源
    if (browser) {
      console.log('\n🧹 清理资源...');
      await browser.close();
      console.log('✅ 浏览器已关闭');
    }
  }
}

// 主函数
async function main() {
  console.log('AutoLoginManager 功能演示');
  console.log('==========================\n');
  
  console.log('这个示例将演示以下功能:');
  console.log('1. 创建 AutoLoginManager 实例');
  console.log('2. 启动浏览器并访问登录页面');
  console.log('3. 使用默认登录逻辑进行自动登录');
  console.log('4. 展示登录结果和配置保存');
  console.log('5. 清理资源\n');
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('按 Enter 键开始演示... ', async () => {
    rl.close();
    await demonstrateAutoLogin();
  });
}

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { demonstrateAutoLogin };
